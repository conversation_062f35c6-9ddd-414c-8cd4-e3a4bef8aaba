// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum ReservationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum RentalStatus {
  ACTIVE
  INACTIVE
  PENDING_APPROVAL
}

enum RegulationType {
  GENERAL
  AMENITY
}

enum ParkingSpotType {
  RESIDENT
  VISITOR
}

enum Status {
  OPEN
  IN_PROGRESS
  BLOCKED
  RESOLVED
}

enum InfractionSeverity {
  MINOR
  MODERATE
  SEVERE
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  ON_HOLD
}

enum PropertyType {
  HOUSE
  DEPARTMENT
}

enum VisitMehtod {
  WALKING
  CAR
}

enum Priority {
  LOW
  MEDIUM
  HIGH
}

enum PackageStatus {
  PENDING
  DELIVERED
  RETURNED
}

model Announcement {
  id        String   @id @default(uuid())
  title     String
  message   String
  imageUrl  String?
  createdAt DateTime @default(now())

  recipients AnnouncementRecipient[]
  role       Role                    @relation(fields: [roleId], references: [id])
  roleId     String
  images     AnnouncementImage[]
}

model AnnouncementImage {
  id             String       @id @default(uuid())
  path           String
  announcementId String
  announcement   Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
}

model AnnouncementRecipient {
  readAt DateTime?

  // Relaciones
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  announcementId String
  announcement   Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)

  // 🔥 Clave primaria compuesta
  @@id([userId, announcementId])
}

model Complaint {
  id              String    @id @default(uuid())
  propertyId      String
  userId          String
  complaintTypeId String
  detail          String
  createdAt       DateTime  @default(now())
  completedAt     DateTime? @default(now())
  status          Status
  priority        Priority

  property      Property         @relation(fields: [propertyId], references: [id])
  user          User             @relation(fields: [userId], references: [id])
  complaintType ComplaintType    @relation(fields: [complaintTypeId], references: [id])
  images        ComplaintImage[] @relation("ComplaintComplaintImage")
}

model ComplaintImage {
  id          String    @id @default(uuid())
  path        String
  complaintId String
  complaint   Complaint @relation("ComplaintComplaintImage", fields: [complaintId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now())
}

model ComplaintType {
  id          String      @id @default(uuid())
  name        String
  description String
  complaint   Complaint[]
}

model Employee {
  id               String   @id @default(uuid())
  firstName        String
  paternalLastName String
  maternalLastName String
  position         String // Posición o cargo del empleado (e.g., "Guardia", "Mantenimiento")
  phone            String?
  email            String?
  hireDate         DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relaciones
  supplierId String?
  supplier   Supplier? @relation(fields: [supplierId], references: [id]) // Proveedor que emplea al trabajador
}

model Event {
  id          String   @id @default(uuid())
  title       String
  description String
  location    String
  startDate   DateTime
  endDate     DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  roles Role[]
}

model Expense {
  id          String   @id @default(uuid())
  description String // Descripción del gasto (e.g., "Reparación de piscina", "Compra de suministros")
  amount      Float // Monto del gasto
  date        DateTime @default(now())
  receipt     String? // URL o ruta al comprobante del gasto (puede ser un archivo o enlace)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Facility {
  id                   String  @id @default(uuid())
  name                 String  @unique
  description          String
  open                 String // formato HH:mm, ej. "08:00"
  close                String // formato HH:mm, ej. "18:00"
  imagePath            String
  reservable           Boolean @default(false) // ¿Permite reservación?
  daysOfWeek           Int[]
  startTime            String?
  endTime              String?
  maxAmountOfPeople    Int?
  maxTimeOfStay        Int?
  maxDateOfReservation Int? //Days

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  reservations Reservation[]
  regulations  Regulation[]
  protocols    Protocol[]
}

model MaintenanceIssueReport {
  id          String   @id @default(uuid())
  description String
  status      Status   @default(OPEN) // Cambiado a enum
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])
  reportedBy String
  user       User      @relation(fields: [reportedBy], references: [id])

  images MaintenanceIssueImage[] @relation("MaintenanceIssueReportMaintenanceIssueImage")
}

model MaintenanceIssueImage {
  id        String                 @id @default(uuid())
  path      String
  reportId  String
  report    MaintenanceIssueReport @relation("MaintenanceIssueReportMaintenanceIssueImage", fields: [reportId], references: [id], onDelete: Cascade)
  createdAt DateTime               @default(now())
}

//Multa
model Fine {
  id          String    @id @default(uuid())
  amount      Float // Monto de la multa
  description String // Descripción de la multa
  issuedAt    DateTime  @default(now())
  isPaid      Boolean   @default(false)
  paidAt      DateTime?

  // Relaciones
  propertyId String
  property   Property    @relation(fields: [propertyId], references: [id])
  images     FineImage[] @relation("FineFineImage")
}

model FineImage {
  id        String   @id @default(uuid())
  path      String
  fineId    String
  fine      Fine     @relation("FineFineImage", fields: [fineId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
}

model Infraction {
  id          String             @id @default(uuid())
  description String // Descripción de la falta
  date        DateTime           @default(now())
  severity    InfractionSeverity

  propertyId String
  property   Property          @relation(fields: [propertyId], references: [id])
  images     InfractionImage[] @relation("InfractionInfractionImage")
}

model InfractionImage {
  id           String     @id @default(uuid())
  path         String
  infractionId String
  infraction   Infraction @relation("InfractionInfractionImage", fields: [infractionId], references: [id], onDelete: Cascade)
  createdAt    DateTime   @default(now())
}

model News {
  id          String   @id @default(uuid())
  title       String
  content     String
  publishedAt DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  isPublished Boolean  @default(false)

  // Relaciones
  userId String?
  user   User?   @relation(fields: [userId], references: [id])
}

model ParkingSpot {
  id          String          @id @default(uuid())
  spotNumber  String          @unique
  isAvailable Boolean         @default(true)
  type        ParkingSpotType
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relaciones
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id])
  visits     Visit[]
}

model Payment {
  id          String   @id @default(uuid())
  amount      Float
  paymentDate DateTime @default(now())
  description String // Ejemplo: "Monthly fee", "Maintenance", etc.
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  propertyId                String?
  property                  Property?                  @relation(fields: [propertyId], references: [id])
  monthlyMaintenanceCharges MonthlyMaintenanceCharge[]
}

model Pet {
  id        String   @id @default(uuid())
  name      String
  type      String // Tipo de mascota (e.g., "Perro", "Gato")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  propertyId String // Relación obligatoria con la propiedad
  property   Property @relation(fields: [propertyId], references: [id])
}

model PhoneDirectory {
  id          String   @id @default(uuid())
  name        String
  phoneNumber String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Property {
  id        String       @id @default(uuid())
  ownerId   String
  address   String
  type      PropertyType
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt

  // Relaciones
  owner                     User                       @relation(fields: [ownerId], references: [id])
  residents                 User[]                     @relation("ResidentsInProperty") // Relación con residentes
  reservations              Reservation[]
  maintenanceIssueReports   MaintenanceIssueReport[]
  rentals                   Rental[]
  infractions               Infraction[]
  payments                  Payment[]
  visits                    Visit[]
  parkingSpots              ParkingSpot[]
  pets                      Pet[] // Relación con mascotas
  vehicles                  Vehicle[] // Relación con vehículos
  tags                      Tag[] // Relación con tags
  fines                     Fine[]
  complaints                Complaint[]
  monthlyMaintenanceCharges MonthlyMaintenanceCharge[]
  packages                  Package[] // Relación con paquetes
}

model Protocol {
  id          String   @id @default(uuid())
  title       String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  facilityId String?
  facility   Facility? @relation(fields: [facilityId], references: [id])
  steps      Step[]
}

model Regulation {
  id        String         @id @default(uuid())
  title     String
  content   String // Contenido del reglamento
  fileUrl   String?
  type      RegulationType
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt

  // Relaciones
  facilityId String? // Relación opcional para reglamentos de amenidades
  facility   Facility? @relation(fields: [facilityId], references: [id])
}

model Rental {
  id          String       @id @default(uuid())
  startDate   DateTime
  endDate     DateTime?
  monthlyRate Float
  status      RentalStatus @default(PENDING_APPROVAL)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relaciones
  propertyId String
  property   Property @relation(fields: [propertyId], references: [id])
}

model Reservation {
  id             String            @id @default(uuid())
  amountOfPeople Int
  startDateTime  DateTime
  endDateTime    DateTime
  status         ReservationStatus @default(PENDING)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt

  authorizedAt DateTime?
  deniedAt     DateTime?
  deniedReason String?

  // Relaciones principales
  propertyId String
  property   Property @relation(fields: [propertyId], references: [id])

  facilityId String
  facility   Facility @relation(fields: [facilityId], references: [id])

  requestedBy String
  user        User   @relation("requestedReservations", fields: [requestedBy], references: [id])

  authorizedBy     String?
  authorizedByUser User?   @relation("authorizedReservations", fields: [authorizedBy], references: [id])

  deniedBy     String?
  deniedByUser User?   @relation("deniedReservations", fields: [deniedBy], references: [id])
}

model Role {
  id          String  @id @default(uuid())
  name        String  @unique
  description String?
  users       User[]  @relation("UserRoles")
  //"RESIDENT", "ADMIN", "COMMITTEE", etc.
  event       Event?  @relation(fields: [eventId], references: [id])
  eventId     String?

  announcements Announcement[]
}

model Service {
  id          String   @id @default(uuid())
  name        String   @unique
  description String
  cost        Float?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  supplierId String
  supplier   Supplier @relation(fields: [supplierId], references: [id])
}

model Step {
  id        String   @id @default(uuid())
  title     String
  order     Int // Para ordenar los pasos dentro del protocolo
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relación con Protocol
  protocolId String // Relación con Protocol
  protocol   Protocol @relation(fields: [protocolId], references: [id])
}

model Supplier {
  id        String   @id @default(uuid())
  name      String
  phone     String?
  email     String?
  address   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  employees Employee[]
  services  Service[]
}

model Tag {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relaciones
  propertyId String // Relación obligatoria con la propiedad
  property   Property @relation(fields: [propertyId], references: [id])
  userId     String? // Relación opcional con un usuario
  user       User?    @relation(fields: [userId], references: [id])
}

model Task {
  id          String     @id @default(uuid())
  title       String // Título o breve descripción de la tarea
  description String? // Descripción detallada de la tarea
  status      TaskStatus @default(PENDING) // Estado actual de la tarea
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  dueDate     DateTime // Fecha límite para completar la tarea
  completedAt DateTime?

  // Relaciones
  userId     String?
  assignedTo User?   @relation(fields: [userId], references: [id]) // Usuario asignado a la tarea
}

model User {
  id                        String   @id @default(uuid())
  email                     String   @unique
  password                  String?
  passwordConfirmed         Boolean  @default(false)
  passwordConfirmationToken String?  @unique
  firstName                 String
  paternalLastName          String
  maternalLastName          String
  phone                     String
  isDeleted                 Boolean  @default(false)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  // Relaciones
  announcementRecipient   AnnouncementRecipient[]
  complaints              Complaint[]
  maintenanceIssueReports MaintenanceIssueReport[]
  newsItems               News[]
  properties              Property[]               @relation("ResidentsInProperty") // Propiedades como residente
  roles                   Role[]                   @relation("UserRoles")
  tags                    Tag[]
  tasks                   Task[]
  property                Property[]
  visits                  Visit[]
  requestedReservations   Reservation[]            @relation("requestedReservations")
  authorizedReservations  Reservation[]            @relation("authorizedReservations")
  deniedReservations      Reservation[]            @relation("deniedReservations")
  packagesReceived        Package[]                @relation("PackagesReceived")
  packagesDelivered       Package[]                @relation("PackagesDelivered")
  packagesDeliveredTo     Package[]                @relation("PackagesDeliveredTo")
  pushTokens              PushToken[]
}

model Vehicle {
  id        String   @id @default(uuid())
  plate     String   @unique
  brand     String
  model     String
  color     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relaciones
  propertyId String // Relación obligatoria con la propiedad
  property   Property @relation(fields: [propertyId], references: [id])
}

model Visit {
  id           String      @id @default(uuid())
  visitorName  String
  visitMethod  VisitMehtod
  vehiclePlate String?
  schedule     DateTime?
  checkInTime  DateTime
  checkOutTime DateTime?
  qrCode       String?
  isQrUsed     Boolean     @default(false)

  // Relaciones
  propertyId    String
  property      Property     @relation(fields: [propertyId], references: [id])
  parkingSpotId String?
  parkingSpot   ParkingSpot? @relation(fields: [parkingSpotId], references: [id])
  requestedBy   String
  user          User         @relation(fields: [requestedBy], references: [id])
}

///////////////////////////////////////////////

model MaintenanceFee {
  id        String   @id @default(uuid())
  year      Int
  amount    Float // Monto mensual fijo durante el año
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Cuotas generadas por mes y propiedad
  monthlyCharges MonthlyMaintenanceCharge[]
}

model MonthlyMaintenanceCharge {
  id               String    @id @default(uuid())
  propertyId       String
  maintenanceFeeId String
  month            Int // 1 - 12
  year             Int
  dueDate          DateTime
  isPaid           Boolean   @default(false)
  paidAt           DateTime?
  lateFeeApplied   Boolean   @default(false)
  lateFeeAmount    Float? // Se guarda si fue aplicada
  waivedLateFee    Boolean   @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  property       Property       @relation(fields: [propertyId], references: [id])
  maintenanceFee MaintenanceFee @relation(fields: [maintenanceFeeId], references: [id])

  paymentId String?
  payment   Payment? @relation(fields: [paymentId], references: [id])
}

model Package {
  id             String        @id @default(uuid())
  number         Int
  status         PackageStatus @default(PENDING)
  deliveryToken  String?       @unique // Token para confirmar entrega
  tokenExpiresAt DateTime? // Fecha de expiración del token
  receivedAt     DateTime      @default(now()) // Fecha de recepción en la administración
  deliveredAt    DateTime? // Fecha de entrega al residente
  notes          String? // Notas adicionales
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // Relaciones
  propertyId String
  property   Property @relation(fields: [propertyId], references: [id])

  // Usuario que recibió el paquete en administración
  receivedBy     String
  receivedByUser User   @relation("PackagesReceived", fields: [receivedBy], references: [id])

  // Usuario que entregó el paquete al residente
  deliveredBy     String?
  deliveredByUser User?   @relation("PackagesDelivered", fields: [deliveredBy], references: [id])

  deliverdTo     String?
  deliverdToUser User?   @relation("PackagesDeliveredTo", fields: [deliverdTo], references: [id])

  images PackageImage[] @relation("PackagePackageImage")
}

model PackageImage {
  id        String   @id @default(uuid())
  path      String
  packageId String
  package   Package  @relation("PackagePackageImage", fields: [packageId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
}

//Incorporar tokens para notificaciones a dispositivos móviles

model PushToken {
  id        String   @id @default(uuid())
  token     String   @unique
  device    String? // Opcional: info como "iOS", "Android", etc.
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lastUsed  DateTime @default(now())

  @@index([userId])
}
