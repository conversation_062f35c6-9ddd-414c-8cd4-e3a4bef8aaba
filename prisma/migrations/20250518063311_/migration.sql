-- CreateTable
CREATE TABLE "FineImage" (
    "id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "fineId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FineImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InfractionImage" (
    "id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "infractionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "InfractionImage_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "FineImage" ADD CONSTRAINT "FineImage_fineId_fkey" FOREIGN KEY ("fineId") REFERENCES "Fine"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InfractionImage" ADD CONSTRAINT "InfractionImage_infractionId_fkey" FOREIGN KEY ("infractionId") REFERENCES "Infraction"("id") ON DELETE CASCADE ON UPDATE CASCADE;
