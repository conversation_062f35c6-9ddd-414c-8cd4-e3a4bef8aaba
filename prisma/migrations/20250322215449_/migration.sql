/*
  Warnings:

  - You are about to drop the column `serviceId` on the `Expense` table. All the data in the column will be lost.
  - You are about to drop the column `supplierId` on the `Expense` table. All the data in the column will be lost.
  - You are about to drop the `_ServiceSuppliers` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `supplierId` to the `Service` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Expense" DROP CONSTRAINT "Expense_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "Expense" DROP CONSTRAINT "Expense_supplierId_fkey";

-- DropForeignKey
ALTER TABLE "_ServiceSuppliers" DROP CONSTRAINT "_ServiceSuppliers_A_fkey";

-- DropForeignKey
ALTER TABLE "_ServiceSuppliers" DROP CONSTRAINT "_ServiceSuppliers_B_fkey";

-- AlterTable
ALTER TABLE "Expense" DROP COLUMN "serviceId",
DROP COLUMN "supplierId";

-- AlterTable
ALTER TABLE "Service" ADD COLUMN     "supplierId" TEXT NOT NULL;

-- DropTable
DROP TABLE "_ServiceSuppliers";

-- AddForeignKey
ALTER TABLE "Service" ADD CONSTRAINT "Service_supplierId_fkey" FOREIGN KEY ("supplierId") REFERENCES "Supplier"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
