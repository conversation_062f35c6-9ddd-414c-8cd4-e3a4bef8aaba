-- CreateTable
CREATE TABLE "MaintenanceIssueImage" (
    "id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "reportId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MaintenanceIssueImage_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MaintenanceIssueImage" ADD CONSTRAINT "MaintenanceIssueImage_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "MaintenanceIssueReport"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
