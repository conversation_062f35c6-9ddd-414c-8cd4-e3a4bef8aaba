/*
  Warnings:

  - You are about to drop the `FacilityIssueReport` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "FacilityIssueReport" DROP CONSTRAINT "FacilityIssueReport_facilityId_fkey";

-- DropForeignKey
ALTER TABLE "FacilityIssueReport" DROP CONSTRAINT "FacilityIssueReport_propertyId_fkey";

-- DropForeignKey
ALTER TABLE "FacilityIssueReport" DROP CONSTRAINT "FacilityIssueReport_reportedBy_fkey";

-- DropTable
DROP TABLE "FacilityIssueReport";

-- CreateTable
CREATE TABLE "MaintenanIssueReport" (
    "id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'OPEN',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "propertyId" TEXT,
    "reportedBy" TEXT NOT NULL,
    "facilityId" TEXT NOT NULL,

    CONSTRAINT "MaintenanIssueReport_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MaintenanIssueReport" ADD CONSTRAINT "MaintenanIssueReport_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanIssueReport" ADD CONSTRAINT "MaintenanIssueReport_reportedBy_fkey" FOREIGN KEY ("reportedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanIssueReport" ADD CONSTRAINT "MaintenanIssueReport_facilityId_fkey" FOREIGN KEY ("facilityId") REFERENCES "Facility"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
