/*
  Warnings:

  - Added the required column `complaintTypeId` to the `Complaint` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Complaint" ADD COLUMN     "complaintTypeId" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "ComplaintType" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "ComplaintType_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Complaint" ADD CONSTRAINT "Complaint_complaintTypeId_fkey" FOREIGN KEY ("complaintTypeId") REFERENCES "ComplaintType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
