/*
  Warnings:

  - You are about to drop the column `description` on the `Complaint` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `Complaint` table. All the data in the column will be lost.
  - The `status` column on the `FacilityIssueReport` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `detail` to the `Complaint` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `status` on the `Complaint` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "Status" AS ENUM ('OPEN', 'IN_PROGRESS', 'BLOCKED', 'RESOLVED');

-- AlterTable
ALTER TABLE "Complaint" DROP COLUMN "description",
DROP COLUMN "type",
ADD COLUMN     "detail" TEXT NOT NULL,
DROP COLUMN "status",
ADD COLUMN     "status" "Status" NOT NULL;

-- AlterTable
ALTER TABLE "FacilityIssueReport" DROP COLUMN "status",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'OPEN';

-- DropEnum
DROP TYPE "FacilityIssueReportStatus";
