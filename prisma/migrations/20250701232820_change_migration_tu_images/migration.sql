/*
  Warnings:

  - You are about to drop the column `deliveredBy` on the `Package` table. All the data in the column will be lost.
  - You are about to drop the column `description` on the `Package` table. All the data in the column will be lost.
  - You are about to drop the column `recipientName` on the `Package` table. All the data in the column will be lost.
  - You are about to drop the column `senderCompany` on the `Package` table. All the data in the column will be lost.
  - You are about to drop the column `senderName` on the `Package` table. All the data in the column will be lost.
  - You are about to drop the column `trackingNumber` on the `Package` table. All the data in the column will be lost.
  - Added the required column `number` to the `Package` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Package" DROP CONSTRAINT "Package_deliveredBy_fkey";

-- AlterTable
ALTER TABLE "Package" DROP COLUMN "deliveredBy",
DROP COLUMN "description",
DROP COLUMN "recipientName",
DROP COLUMN "senderCompany",
DROP COLUMN "senderName",
DROP COLUMN "trackingNumber",
ADD COLUMN     "number" INTEGER NOT NULL;

-- CreateTable
CREATE TABLE "PackageImage" (
    "id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "imageId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PackageImage_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PackageImage" ADD CONSTRAINT "PackageImage_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "Package"("id") ON DELETE CASCADE ON UPDATE CASCADE;
