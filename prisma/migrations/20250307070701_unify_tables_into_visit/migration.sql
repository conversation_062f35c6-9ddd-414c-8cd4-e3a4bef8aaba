/*
  Warnings:

  - You are about to drop the `VisitorAccess` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `VisitorParking` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Visit" DROP CONSTRAINT "Visit_spotId_fkey";

-- DropForeignKey
ALTER TABLE "VisitorAccess" DROP CONSTRAINT "VisitorAccess_visitorParkingId_fkey";

-- DropForeignKey
ALTER TABLE "VisitorParking" DROP CONSTRAINT "VisitorParking_spotId_fkey";

-- AlterTable
ALTER TABLE "Visit" ADD COLUMN     "isUsed" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "parkingSpotId" TEXT,
ADD COLUMN     "qrCode" TEXT,
ALTER COLUMN "vehiclePlate" DROP NOT NULL,
ALTER COLUMN "spotId" DROP NOT NULL;

-- DropTable
DROP TABLE "VisitorAccess";

-- DropTable
DROP TABLE "VisitorParking";

-- AddForeignKey
ALTER TABLE "Visit" ADD CONSTRAINT "Visit_parkingSpotId_fkey" FOREIGN KEY ("parkingSpotId") REFERENCES "ParkingSpot"("id") ON DELETE SET NULL ON UPDATE CASCADE;
