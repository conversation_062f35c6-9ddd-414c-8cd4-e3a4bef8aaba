/*
  Warnings:

  - You are about to drop the column `imageId` on the `PackageImage` table. All the data in the column will be lost.
  - Added the required column `packageId` to the `PackageImage` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "PackageImage" DROP CONSTRAINT "PackageImage_imageId_fkey";

-- AlterTable
ALTER TABLE "PackageImage" DROP COLUMN "imageId",
ADD COLUMN     "packageId" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "PackageImage" ADD CONSTRAINT "PackageImage_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "Package"("id") ON DELETE CASCADE ON UPDATE CASCADE;
