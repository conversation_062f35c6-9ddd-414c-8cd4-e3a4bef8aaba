/*
  Warnings:

  - A unique constraint covering the columns `[imageId]` on the table `Announcement` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Announcement" ADD COLUMN     "imageId" TEXT;

-- CreateTable
CREATE TABLE "AnnouncementImage" (
    "id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AnnouncementImage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Announcement_imageId_key" ON "Announcement"("imageId");

-- AddForeignKey
ALTER TABLE "Announcement" ADD CONSTRAINT "Announcement_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "AnnouncementImage"("id") ON DELETE SET NULL ON UPDATE CASCADE;
