-- CreateEnum
CREATE TYPE "PackageStatus" AS ENUM ('PENDING', 'DELIVERED', 'RETURNED');

-- CreateTable
CREATE TABLE "Package" (
    "id" TEXT NOT NULL,
    "recipientName" TEXT NOT NULL,
    "senderName" TEXT NOT NULL,
    "senderCompany" TEXT,
    "description" TEXT NOT NULL,
    "trackingNumber" TEXT,
    "status" "PackageStatus" NOT NULL DEFAULT 'PENDING',
    "deliveryToken" TEXT,
    "tokenExpiresAt" TIMESTAMP(3),
    "receivedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deliveredAt" TIMESTAMP(3),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "propertyId" TEXT NOT NULL,
    "receivedBy" TEXT NOT NULL,
    "deliveredBy" TEXT,

    CONSTRAINT "Package_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Package_deliveryToken_key" ON "Package"("deliveryToken");

-- AddForeignKey
ALTER TABLE "Package" ADD CONSTRAINT "Package_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Package" ADD CONSTRAINT "Package_receivedBy_fkey" FOREIGN KEY ("receivedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Package" ADD CONSTRAINT "Package_deliveredBy_fkey" FOREIGN KEY ("deliveredBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
