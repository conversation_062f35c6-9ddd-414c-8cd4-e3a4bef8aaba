/*
  Warnings:

  - You are about to drop the column `announcementId` on the `Role` table. All the data in the column will be lost.
  - Added the required column `roleId` to the `Announcement` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Role" DROP CONSTRAINT "Role_announcementId_fkey";

-- AlterTable
ALTER TABLE "Announcement" ADD COLUMN     "roleId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Role" DROP COLUMN "announcementId";

-- AddForeignKey
ALTER TABLE "Announcement" ADD CONSTRAINT "Announcement_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
