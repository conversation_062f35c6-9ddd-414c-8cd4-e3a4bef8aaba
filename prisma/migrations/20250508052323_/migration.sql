/*
  Warnings:

  - You are about to drop the `MaintenanIssueReport` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "MaintenanIssueReport" DROP CONSTRAINT "MaintenanIssueReport_facilityId_fkey";

-- DropForeignKey
ALTER TABLE "MaintenanIssueReport" DROP CONSTRAINT "MaintenanIssueReport_propertyId_fkey";

-- DropForeignKey
ALTER TABLE "MaintenanIssueReport" DROP CONSTRAINT "MaintenanIssueReport_reportedBy_fkey";

-- DropTable
DROP TABLE "MaintenanIssueReport";

-- CreateTable
CREATE TABLE "MaintenanceIssueReport" (
    "id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" "Status" NOT NULL DEFAULT 'OPEN',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "propertyId" TEXT,
    "reportedBy" TEXT NOT NULL,
    "facilityId" TEXT NOT NULL,

    CONSTRAINT "MaintenanceIssueReport_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MaintenanceIssueReport" ADD CONSTRAINT "MaintenanceIssueReport_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanceIssueReport" ADD CONSTRAINT "MaintenanceIssueReport_reportedBy_fkey" FOREIGN KEY ("reportedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MaintenanceIssueReport" ADD CONSTRAINT "MaintenanceIssueReport_facilityId_fkey" FOREIGN KEY ("facilityId") REFERENCES "Facility"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
