/*
  Warnings:

  - The `status` column on the `FacilityIssueReport` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `authorId` on the `News` table. All the data in the column will be lost.
  - You are about to drop the column `notificationId` on the `NotificationUser` table. All the data in the column will be lost.
  - You are about to drop the column `assignedToId` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `copletedAt` on the `Task` table. All the data in the column will be lost.
  - Added the required column `announcmentId` to the `NotificationUser` table without a default value. This is not possible if the table is not empty.
  - Made the column `dueDate` on table `Task` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "FacilityIssueReportStatus" AS ENUM ('OPEN', 'IN_PROGRESS', 'RESOLVED');

-- DropForeignKey
ALTER TABLE "News" DROP CONSTRAINT "News_authorId_fkey";

-- DropForeignKey
ALTER TABLE "NotificationUser" DROP CONSTRAINT "NotificationUser_notificationId_fkey";

-- DropForeignKey
ALTER TABLE "Task" DROP CONSTRAINT "Task_assignedToId_fkey";

-- AlterTable
ALTER TABLE "FacilityIssueReport" DROP COLUMN "status",
ADD COLUMN     "status" "FacilityIssueReportStatus" NOT NULL DEFAULT 'OPEN';

-- AlterTable
ALTER TABLE "News" DROP COLUMN "authorId",
ADD COLUMN     "userId" TEXT;

-- AlterTable
ALTER TABLE "NotificationUser" DROP COLUMN "notificationId",
ADD COLUMN     "announcmentId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Task" DROP COLUMN "assignedToId",
DROP COLUMN "copletedAt",
ADD COLUMN     "completedAt" TIMESTAMP(3),
ADD COLUMN     "userId" TEXT,
ALTER COLUMN "dueDate" SET NOT NULL;

-- DropEnum
DROP TYPE "IncidentStatus";

-- AddForeignKey
ALTER TABLE "News" ADD CONSTRAINT "News_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationUser" ADD CONSTRAINT "NotificationUser_announcmentId_fkey" FOREIGN KEY ("announcmentId") REFERENCES "Announcement"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
