/*
  Warnings:

  - You are about to drop the column `serviceId` on the `FacilityIssueReport` table. All the data in the column will be lost.
  - You are about to drop the column `typeId` on the `FacilityIssueReport` table. All the data in the column will be lost.
  - You are about to drop the column `infractionId` on the `Fine` table. All the data in the column will be lost.
  - You are about to drop the column `spotId` on the `Visit` table. All the data in the column will be lost.
  - You are about to drop the `FacilityIssueReportType` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `facilityId` to the `FacilityIssueReport` table without a default value. This is not possible if the table is not empty.
  - Added the required column `propertyId` to the `Fine` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "FacilityIssueReport" DROP CONSTRAINT "FacilityIssueReport_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "FacilityIssueReport" DROP CONSTRAINT "FacilityIssueReport_typeId_fkey";

-- DropForeignKey
ALTER TABLE "Fine" DROP CONSTRAINT "Fine_infractionId_fkey";

-- AlterTable
ALTER TABLE "FacilityIssueReport" DROP COLUMN "serviceId",
DROP COLUMN "typeId",
ADD COLUMN     "facilityId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Fine" DROP COLUMN "infractionId",
ADD COLUMN     "propertyId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Visit" DROP COLUMN "spotId";

-- DropTable
DROP TABLE "FacilityIssueReportType";

-- AddForeignKey
ALTER TABLE "Fine" ADD CONSTRAINT "Fine_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FacilityIssueReport" ADD CONSTRAINT "FacilityIssueReport_facilityId_fkey" FOREIGN KEY ("facilityId") REFERENCES "Facility"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
