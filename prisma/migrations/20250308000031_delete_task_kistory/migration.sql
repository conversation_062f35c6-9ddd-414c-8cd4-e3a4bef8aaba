/*
  Warnings:

  - You are about to drop the column `isUsed` on the `Visit` table. All the data in the column will be lost.
  - You are about to drop the `TaskHistory` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "TaskHistory" DROP CONSTRAINT "TaskHistory_taskId_fkey";

-- DropForeignKey
ALTER TABLE "TaskHistory" DROP CONSTRAINT "TaskHistory_updatedById_fkey";

-- AlterTable
ALTER TABLE "Task" ADD COLUMN     "copletedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "Visit" DROP COLUMN "isUsed",
ADD COLUMN     "isQrUsed" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "TaskHistory";
