-- CreateTable
CREATE TABLE "MaintenanceFee" (
    "id" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MaintenanceFee_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MonthlyMaintenanceCharge" (
    "id" TEXT NOT NULL,
    "propertyId" TEXT NOT NULL,
    "maintenanceFeeId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "dueDate" TIMESTAMP(3) NOT NULL,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "paidAt" TIMESTAMP(3),
    "lateFeeApplied" BOOLEAN NOT NULL DEFAULT false,
    "lateFeeAmount" DOUBLE PRECISION,
    "waivedLateFee" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "paymentId" TEXT,

    CONSTRAINT "MonthlyMaintenanceCharge_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "MonthlyMaintenanceCharge" ADD CONSTRAINT "MonthlyMaintenanceCharge_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MonthlyMaintenanceCharge" ADD CONSTRAINT "MonthlyMaintenanceCharge_maintenanceFeeId_fkey" FOREIGN KEY ("maintenanceFeeId") REFERENCES "MaintenanceFee"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MonthlyMaintenanceCharge" ADD CONSTRAINT "MonthlyMaintenanceCharge_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "Payment"("id") ON DELETE SET NULL ON UPDATE CASCADE;
