-- CreateTable
CREATE TABLE "_ResidentsInProperty" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_ResidentsInProperty_AB_unique" ON "_ResidentsInProperty"("A", "B");

-- CreateIndex
CREATE INDEX "_ResidentsInProperty_B_index" ON "_ResidentsInProperty"("B");

-- AddForeignKey
ALTER TABLE "_ResidentsInProperty" ADD CONSTRAINT "_ResidentsInProperty_A_fkey" FOREIGN KEY ("A") REFERENCES "Property"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "_ResidentsInProperty" ADD CONSTRAINT "_ResidentsInProperty_B_fkey" FOREIGN KEY ("B") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
