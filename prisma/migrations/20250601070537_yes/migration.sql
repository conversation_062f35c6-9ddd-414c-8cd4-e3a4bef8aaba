/*
  Warnings:

  - You are about to drop the column `imageId` on the `Announcement` table. All the data in the column will be lost.
  - You are about to drop the column `imageUrl` on the `Announcement` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[announcementId]` on the table `AnnouncementImage` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `announcementId` to the `AnnouncementImage` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Announcement" DROP CONSTRAINT "Announcement_imageId_fkey";

-- DropIndex
DROP INDEX "Announcement_imageId_key";

-- AlterTable
ALTER TABLE "Announcement" DROP COLUMN "imageId",
DROP COLUMN "imageUrl";

-- AlterTable
ALTER TABLE "AnnouncementImage" ADD COLUMN     "announcementId" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "AnnouncementImage_announcementId_key" ON "AnnouncementImage"("announcementId");

-- AddForeignKey
ALTER TABLE "AnnouncementImage" ADD CONSTRAINT "AnnouncementImage_announcementId_fkey" FOREIGN KEY ("announcementId") REFERENCES "Announcement"("id") ON DELETE CASCADE ON UPDATE CASCADE;
