/*
  Warnings:

  - You are about to drop the `Incident` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `IncidentType` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Incident" DROP CONSTRAINT "Incident_propertyId_fkey";

-- DropForeignKey
ALTER TABLE "Incident" DROP CONSTRAINT "Incident_reportedBy_fkey";

-- DropForeignKey
ALTER TABLE "Incident" DROP CONSTRAINT "Incident_serviceId_fkey";

-- DropForeignKey
ALTER TABLE "Incident" DROP CONSTRAINT "Incident_typeId_fkey";

-- DropTable
DROP TABLE "Incident";

-- DropTable
DROP TABLE "IncidentType";

-- CreateTable
CREATE TABLE "FacilityIssueReportType" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "FacilityIssueReportType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FacilityIssueReport" (
    "id" TEXT NOT NULL,
    "propertyId" TEXT,
    "reportedBy" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" "IncidentStatus" NOT NULL DEFAULT 'OPEN',
    "typeId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "serviceId" TEXT,

    CONSTRAINT "FacilityIssueReport_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FacilityIssueReportType_name_key" ON "FacilityIssueReportType"("name");

-- AddForeignKey
ALTER TABLE "FacilityIssueReport" ADD CONSTRAINT "FacilityIssueReport_propertyId_fkey" FOREIGN KEY ("propertyId") REFERENCES "Property"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FacilityIssueReport" ADD CONSTRAINT "FacilityIssueReport_reportedBy_fkey" FOREIGN KEY ("reportedBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FacilityIssueReport" ADD CONSTRAINT "FacilityIssueReport_typeId_fkey" FOREIGN KEY ("typeId") REFERENCES "FacilityIssueReportType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FacilityIssueReport" ADD CONSTRAINT "FacilityIssueReport_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "Service"("id") ON DELETE SET NULL ON UPDATE CASCADE;
