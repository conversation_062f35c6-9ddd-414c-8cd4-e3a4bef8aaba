/*
  Warnings:

  - Added the required column `endTime` to the `Facility` table without a default value. This is not possible if the table is not empty.
  - Added the required column `scheduleEndTime` to the `Facility` table without a default value. This is not possible if the table is not empty.
  - Added the required column `scheduleStartTime` to the `Facility` table without a default value. This is not possible if the table is not empty.
  - Added the required column `startTime` to the `Facility` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Facility" ADD COLUMN     "daysOfWeek" INTEGER[],
ADD COLUMN     "endTime" TEXT NOT NULL,
ADD COLUMN     "imageUrl" TEXT,
ADD COLUMN     "reservable" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "scheduleEndTime" TEXT NOT NULL,
ADD COLUMN     "scheduleStartTime" TEXT NOT NULL,
ADD COLUMN     "startTime" TEXT NOT NULL;
