// prisma/seedRoles.ts
import { PrismaClient } from '@prisma/client';

export async function rolesSeeder(prisma: PrismaClient) {
  console.log('Roles seeder started');

  const roles = ['ADMIN', 'RESIDENT', 'COMMITTEE', 'OWNER', 'RENTAL'];

  for (const roleName of roles) {
    await prisma.role.upsert({
      where: { name: roleName },
      update: {},
      create: {
        name: roleName,
      },
    });
  }
  console.log('Roles seeder finished');
}
