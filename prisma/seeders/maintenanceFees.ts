import { PrismaClient } from '@prisma/client';

/**
 * Seeder para generar cuotas de mantenimiento y cargos mensuales para 2024-2025
 * @param prisma Cliente de Prisma pasado desde la función principal de seed
 */
export async function seedMaintenanceFees(prisma: PrismaClient) {
  console.log('Iniciando seed de cuotas de mantenimiento 2024-2025...');

  try {
    // 1. Verificar que existan propiedades en la base de datos
    const propertiesCount = await prisma.property.count();

    if (propertiesCount === 0) {
      console.log('No hay propiedades en la base de datos. Por favor, crea propiedades primero.');
      return;
    }

    console.log(`Se encontraron ${propertiesCount} propiedades.`);

    // 2. Obtener todas las propiedades
    const properties = await prisma.property.findMany();

    // 3. Crear cuotas de mantenimiento para 2024 y 2025 si no existen
    const maintenanceFee2024 = await createOrFindMaintenanceFee(prisma, 2024, 1400.0);
    const maintenanceFee2025 = await createOrFindMaintenanceFee(prisma, 2025, 1400.0);

    console.log(`Cuota de mantenimiento 2024: $${maintenanceFee2024.amount} mensuales`);
    console.log(`Cuota de mantenimiento 2025: $${maintenanceFee2025.amount} mensuales`);

    // 4. Generar cargos mensuales para cada propiedad para ambos años
    console.log('Generando cargos mensuales para cada propiedad...');

    // Para 2024
    let created2024 = 0;
    let skipped2024 = 0;
    for (const property of properties) {
      const result = await generateMonthlyChargesForProperty(prisma, property.id, maintenanceFee2024.id, 2024);
      created2024 += result.created;
      skipped2024 += result.skipped;
    }

    // Para 2025
    let created2025 = 0;
    let skipped2025 = 0;
    for (const property of properties) {
      const result = await generateMonthlyChargesForProperty(prisma, property.id, maintenanceFee2025.id, 2025);
      created2025 += result.created;
      skipped2025 += result.skipped;
    }

    console.log(`2024: ${created2024} cargos mensuales creados, ${skipped2024} ya existían.`);
    console.log(`2025: ${created2025} cargos mensuales creados, ${skipped2025} ya existían.`);

    console.log('Seed de cuotas de mantenimiento completado exitosamente.');
  } catch (error) {
    console.error('Error durante el proceso de seed de cuotas de mantenimiento:', error);
  }
}

/**
 * Crea o encuentra una cuota de mantenimiento para un año específico
 */
async function createOrFindMaintenanceFee(prisma: PrismaClient, year: number, amount: number) {
  // Buscar si ya existe una cuota para este año
  const existingFee = await prisma.maintenanceFee.findFirst({
    where: { year },
  });

  if (existingFee) {
    console.log(`Ya existe una cuota para el año ${year}.`);
    return existingFee;
  }

  // Crear nueva cuota de mantenimiento
  console.log(`Creando nueva cuota para el año ${year}...`);
  return await prisma.maintenanceFee.create({
    data: {
      year,
      amount,
    },
  });
}

/**
 * Genera cargos mensuales para una propiedad específica
 */
async function generateMonthlyChargesForProperty(
  prisma: PrismaClient,
  propertyId: string,
  maintenanceFeeId: string,
  year: number,
) {
  let created = 0;
  let skipped = 0;

  // Para cada mes del año
  for (let month = 1; month <= 12; month++) {
    // Verificar si ya existe un cargo para esta propiedad en este mes y año
    const existingCharge = await prisma.monthlyMaintenanceCharge.findFirst({
      where: {
        propertyId,
        maintenanceFeeId,
        month,
        year,
      },
    });

    if (existingCharge) {
      skipped++;
      continue;
    }

    // Calcular fecha de vencimiento (día 10 de cada mes)
    const dueDate = new Date(year, month - 1, 10);

    // Crear cargo mensual
    await prisma.monthlyMaintenanceCharge.create({
      data: {
        propertyId,
        maintenanceFeeId,
        month,
        year,
        dueDate,
        isPaid: false,
        lateFeeApplied: false,
        waivedLateFee: false,
      },
    });

    created++;
  }

  return { created, skipped };
}
