import { PrismaClient, ParkingSpotType } from '@prisma/client';

export async function parkingSpotsSeeder(prisma: PrismaClient) {
  console.log('Parking spots seeder started');

  const properties = await prisma.property.findMany();

  if (!properties.length) {
    console.error('No properties found. Please seed properties first.');
    return;
  }

  const porpertiesSpots = properties.flatMap((property) => {
    const spots = [
      {
        spotNumber: `${property.address}-E1`,
        isAvailable: true,
        type: ParkingSpotType.RESIDENT,
        propertyId: property.id,
      },
      {
        spotNumber: `${property.address}-E2`,
        isAvailable: true,
        type: ParkingSpotType.RESIDENT,
        propertyId: property.id,
      },
    ];
    return spots;
  });

  const visitorSpots = Array.from({ length: 21 }, (_, i) => ({
    spotNumber: `EV-${i + 1}`,
    isAvailable: true,
    type: ParkingSpotType.VISITOR,
  }));

  const parkingSpots = [...porpertiesSpots, ...visitorSpots];

  for (const spot of parkingSpots) {
    await prisma.parkingSpot.upsert({
      where: { spotNumber: spot.spotNumber },
      update: {},
      create: spot,
    });
  }

  console.log('Parking spots seeder finished');
}
