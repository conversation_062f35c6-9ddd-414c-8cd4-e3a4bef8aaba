import { PrismaClient, PropertyType } from '@prisma/client';

export async function propertiesSeeder(prisma: PrismaClient) {
  console.log('Properties seeder started');

  const existingProperties = await prisma.property.findMany();
  if (existingProperties.length > 0) {
    console.log('Seed skipped: Properties already exist.');
    return;
  }

  const builder = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  const properties = Array.from({ length: 119 }, (_, i) => ({
    address: `${i + 1}`,
    ownerId: builder.id,
    type: PropertyType.HOUSE,
  }));

  const departments = [
    { address: '100A', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '101A', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '102A', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '103A', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '200', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '201', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '202', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '203', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '204', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '205', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '300', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '301', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '302', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '303', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '304', ownerId: builder.id, type: PropertyType.DEPARTMENT },
    { address: '305', ownerId: builder.id, type: PropertyType.DEPARTMENT },
  ];

  const allProperties = [...properties, ...departments];

  await prisma.property.createMany({
    data: allProperties,
  });

  console.log('Properties seeder finished');
}
