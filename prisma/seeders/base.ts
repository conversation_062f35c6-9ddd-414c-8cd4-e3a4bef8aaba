import { PrismaClient } from '@prisma/client';
import { rolesSeeder } from './roles';
import { adminSeeder } from './admin';
import { propertiesSeeder } from './properties';
import { parkingSpotsSeeder } from './parking-spots';
import { seedMaintenanceFees } from './maintenanceFees';

const prisma = new PrismaClient();

async function main() {
  await rolesSeeder(prisma);
  await adminSeeder(prisma);
  await propertiesSeeder(prisma);
  await parkingSpotsSeeder(prisma);
  await seedMaintenanceFees(prisma);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
