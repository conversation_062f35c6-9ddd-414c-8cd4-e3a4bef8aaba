import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

export async function adminSeeder(prisma: PrismaClient) {
  console.log('Admin seeder started');

  const existingAdmin = await prisma.user.findUnique({
    where: { email: '<EMAIL>' },
  });

  if (existingAdmin) {
    console.log('Admin already exists');
    return;
  }

  const adminRole = await prisma.role.findUnique({
    where: { name: 'ADMIN' },
  });

  if (!adminRole) {
    throw new Error('El rol ADMIN no está definido. Ejecute el seed de roles primero.');
  }
  const hashedPassword = await bcrypt.hash('fa8s7fta8sdfagsjhbf8a7dtf', 10);

  await prisma.user.create({
    data: {
      firstName: 'Ricardo',
      paternalLastName: 'Fuentes',
      maternalLastName: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      password: hashedPassword,
      passwordConfirmationToken: null,
      passwordConfirmed: true,
      phone: '4425611116',
      roles: {
        connect: { id: adminRole.id },
      },
    },
  });

  await prisma.user.create({
    data: {
      firstName: 'Sabino',
      paternalLastName: 'Zibtá',
      maternalLastName: '',
      email: '<EMAIL>',
      password: hashedPassword,
      passwordConfirmationToken: null,
      passwordConfirmed: true,
      phone: '4425611116',
      roles: {
        connect: { id: adminRole.id },
      },
    },
  });

  console.log('Admin seeder finished');
}
