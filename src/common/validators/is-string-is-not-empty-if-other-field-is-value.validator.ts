import { registerDecorator, ValidationOptions, ValidationArguments, isString, isNotEmpty } from 'class-validator';

export function IsStringAndNotEmptyIfOtherFieldIsValue(
  property: string,
  expectedValue: any,
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'isStringAndNotEmptyIfOtherFieldIsValue',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property, expectedValue],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName, relatedValueExpected] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          if (relatedValue === relatedValueExpected) {
            return isString(value) && isNotEmpty(value);
          }
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a non-empty string when ${args.constraints[0]} is '${args.constraints[1]}'`;
        },
      },
    });
  };
}
