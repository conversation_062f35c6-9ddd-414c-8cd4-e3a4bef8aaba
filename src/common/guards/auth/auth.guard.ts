import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    // Usa cookie-parser para obtener la cookie 'accessToken'
    const token = request.cookies['accessToken']; // <PERSON>qu<PERSON> accedes al token JWT desde la cookie

    if (!token) {
      throw new UnauthorizedException('No access token provided');
    }

    try {
      // Verifica el token usando JwtService
      const payload = await this.jwtService.verifyAsync(token);
      const typedRequest: any = request;
      typedRequest.user = payload; // Almacena el payload del token en la solicitud
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}
