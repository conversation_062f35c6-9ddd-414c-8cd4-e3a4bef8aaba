import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthResult } from 'src/modules/auth/auth.types';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true; // Si no se requieren roles específicos, permitir el acceso
    }

    const { user } = context.switchToHttp().getRequest();
    const typedUser = user as AuthResult;
    return typedUser.roles.some((role) => requiredRoles.includes(role)); // Verificar si el rol del usuario está en la lista de roles permitidos
  }
}
