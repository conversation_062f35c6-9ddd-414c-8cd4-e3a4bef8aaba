import { ConfigService } from '@nestjs/config';
/* eslint-disable */
// require('newrelic');
/* eslint-enable */
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from 'nestjs-pino';
import { Config } from './config/configuration';
import { CONST } from './config/constants';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import { PrismaService } from './modules/database/prisma/prisma.service';
import * as cookieParser from 'cookie-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { bufferLogs: true });

  //Logger
  app.useLogger(app.get(Logger));

  //Config
  const configService = app.get(ConfigService);
  const config = configService.get<Config>(CONST.CONFIG_KEY);

  // CORS
  app.enableCors({
    origin:
      config.environment === CONST.DEVELOPMENT || config.environment === CONST.STAGING
        ? [/http:\/\/localhost:*/, new RegExp(config.corsOrigin)]
        : RegExp(config.corsOrigin),
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization'],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  });

  //Pipes
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  //Swagger
  const swaggerConfiguration = new DocumentBuilder()
    .setTitle('HOA Management System API')
    .setDescription('API Rest homeowners management system.')
    .setVersion('1.0')
    .addApiKey({ type: 'apiKey', in: 'header', name: 'x-api-key' }, 'API-KEY')
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfiguration);
  SwaggerModule.setup('api', app, document);

  //Body Parser
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  app.use(cookieParser());

  //Prisma
  const prismaService: PrismaService = app.get(PrismaService);
  prismaService.enableShutdownHooks(app);

  //App
  await app.listen(3000);
}

bootstrap();
