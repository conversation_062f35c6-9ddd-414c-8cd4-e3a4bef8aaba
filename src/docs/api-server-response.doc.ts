import { ServerErrorType } from 'src/modules/error-handler/server-error.type';
import { ServiceErrorType } from 'src/modules/error-handler/service-error.type';

export const internalServerErrorResponseDoc = {
  status: 500,
  description: 'Cuando ocurra un error interno en el servidor',
  type: ServerErrorType,
};

export const porviderExceptionErrorResponseDoc = {
  statusCode: 400,
  description: 'Respuesta si el proveedor devuelve excepción',
  type: ServiceErrorType,
};
