import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreatePackageDto } from '../business-logic/package/dto/create-package.dto';
import { UpdatePackageDto } from '../business-logic/package/dto/update-package.dto';
import { PackageStatus } from '@prisma/client';
import { DateTime } from 'luxon';

@Injectable()
export class PackagePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un paquete
  async create(data: CreatePackageDto, receivedBy: string) {
    return this.prisma.package.create({
      data: {
        number: data.number,
        status: PackageStatus.PENDING,
        receivedAt: DateTime.now().toISO(),
        receivedBy: receivedBy,
        notes: data.notes,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Obtener todos los paquetes
  async findAll() {
    return this.prisma.package.findMany({
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        deliveredByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        images: true,
      },
    });
  }

  // 🔹 Obtener un paquete por ID
  async findById(packageId: string) {
    return this.prisma.package.findUnique({
      where: { id: packageId },
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        deliveredByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        deliverdToUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        images: true,
      },
    });
  }

  // 🔹 Actualizar un paquete
  async update(packageId: string, data: UpdatePackageDto) {
    return this.prisma.package.update({
      where: { id: packageId },
      data: {
        number: data.number,
        notes: data.notes,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Eliminar un paquete
  async delete(packageId: string) {
    return this.prisma.package.delete({
      where: { id: packageId },
    });
  }

  // 🔹 Generar token de entrega
  async generateDeliveryToken(packageId: string, token: string, expiresAt: Date) {
    return this.prisma.package.update({
      where: { id: packageId },
      data: {
        deliveryToken: token,
        tokenExpiresAt: expiresAt,
      },
    });
  }

  // 🔹 Entregar paquete con token
  async deliverPackage(packageId: string, deliveredBy: string) {
    return this.prisma.package.update({
      where: { id: packageId },
      data: {
        status: PackageStatus.DELIVERED,
        deliveredAt: new Date(),
        deliveredBy: deliveredBy,
        deliveryToken: null,
        tokenExpiresAt: null,
        deliverdTo: deliveredBy,
      },
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        deliveredByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
      },
    });
  }

  // 🔹 Buscar paquete por token de entrega
  async findByDeliveryToken(token: string) {
    return this.prisma.package.findUnique({
      where: { deliveryToken: token },
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
      },
    });
  }

  // 🔹 Obtener paquetes por propiedad
  async findByPropertyId(propertyId: string) {
    return this.prisma.package.findMany({
      where: { propertyId },
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        deliveredByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        images: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  // 🔹 Obtener paquetes pendientes
  async findPending() {
    return this.prisma.package.findMany({
      where: { status: PackageStatus.PENDING },
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        images: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  // 🔹 Obtener paquetes pendientes por propiedad
  async findPendingByPropertyId(propertyId: string) {
    return this.prisma.package.findMany({
      where: { propertyId, status: PackageStatus.PENDING },
      include: {
        property: {
          select: {
            id: true,
            address: true,
          },
        },
        receivedByUser: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
          },
        },
        images: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  //Configurar manejo de imagenes como en el caso de quejas
  async saveImagePath(packageId: string, path: string) {
    return this.prisma.packageImage.create({
      data: {
        packageId,
        path,
      },
    });
  }

  async deleteAllImagesForPackage(packageId: string) {
    await this.prisma.packageImage.deleteMany({
      where: { packageId },
    });
  }

  async deleteImageById(imageId: string) {
    return this.prisma.packageImage.delete({
      where: { id: imageId },
    });
  }
}
