import { Injectable } from '@nestjs/common';
import { CreateMaintenanceFeeDto } from '../business-logic/maintenance-fee/dto/create-maintenance-fee.dto';
import { UpdateMaintenanceFeeDto } from '../business-logic/maintenance-fee/dto/update-maintenance-fee.dto';
import { DateTime } from 'luxon';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class MaintenanceFeePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateMaintenanceFeeDto) {
    const properties = await this.prisma.property.findMany();

    const monthlyCharges = properties.flatMap((property) =>
      Array.from({ length: 12 }, (_, index) => {
        const month = index + 1;
        const dueDate = DateTime.fromObject({
          year: data.year,
          month,
          day: 10,
        }).toJSDate();

        return {
          month,
          year: data.year,
          dueDate,
          propertyId: property.id,
          isPaid: false,
          lateFeeApplied: false,
          waivedLateFee: false,
        };
      }),
    );

    return this.prisma.maintenanceFee.create({
      data: {
        year: data.year,
        amount: data.amount,
        monthlyCharges: {
          create: monthlyCharges,
        },
      },
      include: {
        monthlyCharges: true,
      },
    });
  }

  async findAll() {
    return this.prisma.maintenanceFee.findMany({});
  }

  async findById(id: string) {
    return this.prisma.maintenanceFee.findUnique({
      where: { id },
      include: {
        monthlyCharges: true,
      },
    });
  }

  async update(id: string, data: UpdateMaintenanceFeeDto) {
    return this.prisma.maintenanceFee.update({
      where: { id },
      data,
    });
  }

  async delete(id: string) {
    return this.prisma.maintenanceFee.delete({
      where: { id },
    });
  }
}
