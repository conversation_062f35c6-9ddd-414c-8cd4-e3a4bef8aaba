import { Injectable } from '@nestjs/common';
import { Facility } from '@prisma/client';
import { CreateFacilityDto } from 'src/modules/business-logic/facility/dto/create-facility.dto';
import { FacilityResponseDto } from 'src/modules/business-logic/facility/dto/response-facility.dto';
import { UpdateFacilityDto } from 'src/modules/business-logic/facility/dto/update-facility.dto';
import { PrismaService } from '../database/prisma/prisma.service';
import { FacilitySelectDto } from '../business-logic/facility/dto/facility-select.dto';
import { FacilityMobileResponseDto } from '../business-logic/facility/dto/facility-mobile-response.dto';

@Injectable()
export class FacilityPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createFacilityDto: CreateFacilityDto): Promise<FacilityResponseDto> {
    try {
      return await this.prisma.facility.create({
        data: {
          ...createFacilityDto,
          imagePath: createFacilityDto.imagePath,
        },
      });
    } catch (error) {
      console.log(error);
    }
  }

  async findById(id: Facility['id']): Promise<FacilityResponseDto | null> {
    return await this.prisma.facility.findUnique({
      where: { id },
      include: {
        reservations: true,
        regulations: true,
        protocols: true,
      },
    });
  }

  async findAll(): Promise<FacilityResponseDto[]> {
    return await this.prisma.facility.findMany({
      include: {
        reservations: true,
        regulations: true,
        protocols: true,
      },
    });
  }

  async findAllForMobile(): Promise<FacilityMobileResponseDto[]> {
    return await this.prisma.facility.findMany({
      select: {
        id: true,
        name: true,
        open: true,
        close: true,
        imagePath: true,
        reservable: true,
        daysOfWeek: true,
        startTime: true,
        endTime: true,
        maxAmountOfPeople: true,
        maxTimeOfStay: true,
        maxDateOfReservation: true,
      },
    });
  }

  async findAllForSelect(): Promise<FacilitySelectDto[]> {
    return await this.prisma.facility.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  }

  async update(id: Facility['id'], updateFacilityDto: UpdateFacilityDto): Promise<FacilityResponseDto> {
    return await this.prisma.facility.update({
      where: { id },
      data: updateFacilityDto,
    });
  }

  async delete(id: Facility['id']): Promise<FacilityResponseDto> {
    return await this.prisma.facility.delete({
      where: { id },
    });
  }
}
