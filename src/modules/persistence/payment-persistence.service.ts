import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreatePaymentDto } from '../business-logic/payment/dto/create-payment.dto';
import { UpdatePaymentDto } from '../business-logic/payment/dto/update-payment.dto';

@Injectable()
export class PaymentPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un pago
  async createPayment(data: CreatePaymentDto) {
    return this.prisma.payment.create({
      data: {
        amount: data.amount,
        paymentDate: data.paymentDate ?? new Date(),
        description: data.description,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Obtener todos los pagos
  async findAllPayments() {
    return this.prisma.payment.findMany({
      include: {
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Obtener un pago por ID
  async getPaymentById(paymentId: string) {
    return this.prisma.payment.findUnique({
      where: { id: paymentId },
      include: {
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Actualizar un pago
  async updatePayment(paymentId: string, data: UpdatePaymentDto) {
    return this.prisma.payment.update({
      where: { id: paymentId },
      data,
    });
  }

  // 🔹 Eliminar un pago
  async deletePayment(paymentId: string) {
    return this.prisma.payment.delete({
      where: { id: paymentId },
    });
  }
}
