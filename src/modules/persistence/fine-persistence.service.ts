import { Injectable } from '@nestjs/common';
import { CreateFineDto } from 'src/modules/business-logic/fine/dto/create-fine.dto';
import { UpdateFineDto } from 'src/modules/business-logic/fine/dto/update-fine.dto';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class FinePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una multa
  async createFine(data: CreateFineDto) {
    return this.prisma.fine.create({
      data: {
        amount: data.amount,
        description: data.description,
        issuedAt: new Date(),
        isPaid: data.isPaid ?? false,
        paidAt: data.paidAt,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Obtener todas las multas
  async findAllFines() {
    return this.prisma.fine.findMany({
      include: {
        images: true,
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Obtener una multa por ID
  async getFineById(fineId: string) {
    return this.prisma.fine.findUnique({
      where: { id: fineId },
      include: {
        images: true,
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Actualizar una multa
  async updateFine(fineId: string, data: UpdateFineDto) {
    return this.prisma.fine.update({
      where: { id: fineId },
      data: {
        amount: data.amount,
        description: data.description,
        isPaid: data.isPaid,
        paidAt: data.paidAt,
      },
    });
  }

  // 🔹 Eliminar una multa
  async deleteFine(fineId: string) {
    return this.prisma.fine.delete({
      where: { id: fineId },
    });
  }

  async saveImagePath(fineId: string, path: string) {
    return this.prisma.fineImage.create({
      data: {
        fineId,
        path,
      },
    });
  }

  async deleteAllImagesForFinet(fineId: string) {
    await this.prisma.fineImage.deleteMany({
      where: { fineId },
    });
  }

  async deleteImageById(imageId: string) {
    return this.prisma.fineImage.delete({
      where: { id: imageId },
    });
  }
}
