import { Injectable } from '@nestjs/common';
import { CreateServiceDto } from '../business-logic/service/dto/create-service.dto';
import { UpdateServiceDto } from '../business-logic/service/dto/update-service.dto';
import { ServiceSelectDto } from '../business-logic/service/dto/service-select.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class ServicePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un servicio
  async create(data: CreateServiceDto) {
    return this.prisma.service.create({
      data: {
        name: data.name,
        description: data.description || null,
        cost: data.cost || null,
        supplierId: data.supplierId,
      },
    });
  }

  // 🔹 Obtener todos los servicios
  async findAll() {
    return this.prisma.service.findMany();
  }

  async findAllForSelect(): Promise<ServiceSelectDto[]> {
    return await this.prisma.service.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  }

  // 🔹 Obtener un servicio por ID
  async findById(serviceId: string) {
    return this.prisma.service.findUnique({
      where: { id: serviceId },
    });
  }

  // 🔹 Actualizar un servicio
  async update(serviceId: string, data: UpdateServiceDto) {
    return this.prisma.service.update({
      where: { id: serviceId },
      data,
    });
  }

  // 🔹 Eliminar un servicio
  async delete(serviceId: string) {
    return this.prisma.service.delete({
      where: { id: serviceId },
    });
  }
}
