import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreateVehicleDto } from '../business-logic/vehicle/dto/create-vehicle.dto';
import { UpdateVehicleDto } from '../business-logic/vehicle/dto/update-vehicle.dto';

@Injectable()
export class VehiclePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateVehicleDto) {
    return this.prisma.vehicle.create({
      data: {
        plate: data.plate,
        brand: data.brand,
        model: data.model,
        color: data.color,
        propertyId: data.propertyId,
      },
    });
  }

  async findAll() {
    return this.prisma.vehicle.findMany({
      include: {
        property: true,
      },
    });
  }

  async findById(vehicleId: string) {
    return this.prisma.vehicle.findUnique({
      where: { id: vehicleId },
      include: {
        property: true,
      },
    });
  }

  async update(vehicleId: string, data: UpdateVehicleDto) {
    return this.prisma.vehicle.update({
      where: { id: vehicleId },
      data: { ...data },
    });
  }

  async delete(vehicleId: string) {
    return this.prisma.vehicle.delete({
      where: { id: vehicleId },
    });
  }
}
