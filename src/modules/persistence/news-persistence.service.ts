import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateNewsDto } from '../business-logic/news/dto/create-news.dto';
import { UpdateNewsDto } from '../business-logic/news/dto/update-news.dto';

@Injectable()
export class NewsPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una noticia
  async createNews(data: CreateNewsDto) {
    return this.prisma.news.create({
      data: {
        title: data.title,
        content: data.content,
        publishedAt: data.publishedAt ?? new Date(),
        isPublished: data.isPublished ?? false,
        userId: data.userId,
      },
    });
  }

  // 🔹 Obtener todas las noticias
  async findAllNews() {
    return this.prisma.news.findMany({
      include: {
        user: { select: { id: true, firstName: true, email: true } },
      },
    });
  }

  // 🔹 Obtener una noticia por ID
  async getNewsById(newsId: string) {
    return this.prisma.news.findUnique({
      where: { id: newsId },
      include: {
        user: { select: { id: true, firstName: true, email: true } },
      },
    });
  }

  // 🔹 Actualizar una noticia
  async updateNews(newsId: string, data: UpdateNewsDto) {
    return this.prisma.news.update({
      where: { id: newsId },
      data,
    });
  }

  // 🔹 Eliminar una noticia
  async deleteNews(newsId: string) {
    return this.prisma.news.delete({
      where: { id: newsId },
    });
  }
}
