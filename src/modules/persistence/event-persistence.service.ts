import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateEventDto } from '../business-logic/event/dto/create-event.dto';
import { UpdateEventDto } from '../business-logic/event/dto/update-event.dto';

@Injectable()
export class EventPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un evento
  async createEvent(data: CreateEventDto) {
    return this.prisma.event.create({
      data: {
        title: data.title,
        description: data.description,
        location: data.location,
        startDate: data.startDate,
        endDate: data.endDate,
        roles: {
          connect: data.roles?.map((id) => ({ id })) ?? [],
        },
      },
    });
  }

  // 🔹 Obtener todos los eventos
  async findAllEvents() {
    return this.prisma.event.findMany();
  }

  // 🔹 Obtener un evento por ID
  async getEventById(eventId: string) {
    return this.prisma.event.findUnique({
      where: { id: eventId },
    });
  }

  // 🔹 Actualizar un evento
  async updateEvent(eventId: string, data: UpdateEventDto) {
    return this.prisma.event.update({
      where: { id: eventId },
      data: {
        title: data.title,
        description: data.description,
        location: data.location,
        startDate: data.startDate,
        endDate: data.endDate,
        roles: {
          set: data.roles?.map((id) => ({ id })) ?? [],
        },
      },
    });
  }

  // 🔹 Eliminar un evento
  async deleteEvent(eventId: string) {
    return this.prisma.event.delete({
      where: { id: eventId },
    });
  }
}
