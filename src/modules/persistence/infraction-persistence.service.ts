import { Injectable } from '@nestjs/common';
import { CreateInfractionDto } from 'src/modules/business-logic/infraction/dto/create-infraction.dto';
import { UpdateInfractionDto } from 'src/modules/business-logic/infraction/dto/update-infraction.dto';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class InfractionPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una infracción
  async createInfraction(data: CreateInfractionDto) {
    return this.prisma.infraction.create({
      data: {
        description: data.description,
        date: data.date ?? new Date(),
        severity: data.severity,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Obtener todas las infracciones
  async findAllInfractions() {
    return this.prisma.infraction.findMany({
      include: {
        images: true,
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Obtener una infracción por ID
  async getInfractionById(infractionId: string) {
    return this.prisma.infraction.findUnique({
      where: { id: infractionId },
      include: {
        images: true,
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Actualizar una infracción
  async updateInfraction(infractionId: string, data: UpdateInfractionDto) {
    return this.prisma.infraction.update({
      where: { id: infractionId },
      data: {
        description: data.description,
        date: data.date ?? new Date(),
        severity: data.severity,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Eliminar una infracción
  async deleteInfraction(infractionId: string) {
    return this.prisma.infraction.delete({
      where: { id: infractionId },
    });
  }

  async saveImagePath(infractionId: string, path: string) {
    return this.prisma.infractionImage.create({
      data: {
        infractionId,
        path,
      },
    });
  }

  async deleteAllImagesForReport(infractionId: string) {
    await this.prisma.infractionImage.deleteMany({
      where: { infractionId },
    });
  }

  async deleteImageById(imageId: string) {
    return this.prisma.infractionImage.delete({
      where: { id: imageId },
    });
  }
}
