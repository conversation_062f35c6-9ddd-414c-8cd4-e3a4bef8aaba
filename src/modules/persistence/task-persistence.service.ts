import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreateTaskDto } from '../business-logic/task/dto/create-task.dto';
import { UpdateTaskDto } from '../business-logic/task/dto/update-task.dto';

@Injectable()
export class TaskPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una tarea
  async create(data: CreateTaskDto) {
    return this.prisma.task.create({
      data: {
        title: data.title,
        description: data.description || null,
        status: data.status,
        dueDate: data.dueDate,
        userId: data.userId || null,
      },
    });
  }

  // 🔹 Obtener todas las tareas
  async findAll() {
    return this.prisma.task.findMany();
  }

  // 🔹 Obtener una tarea por ID
  async findById(taskId: string) {
    return this.prisma.task.findUnique({
      where: { id: taskId },
    });
  }

  // 🔹 Actualizar una tarea
  async update(taskId: string, data: UpdateTaskDto) {
    return this.prisma.task.update({
      where: { id: taskId },
      data,
    });
  }

  // 🔹 Eliminar una tarea
  async delete(taskId: string) {
    return this.prisma.task.delete({
      where: { id: taskId },
    });
  }
}
