import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateComplaintDto } from '../business-logic/complaint/dto/create-complaint.dto';
import { UpdateComplaintDto } from '../business-logic/complaint/dto/update-complaint.dto';

@Injectable()
export class ComplaintPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una queja
  async createComplaint(data: CreateComplaintDto) {
    return this.prisma.complaint.create({
      data: {
        userId: data.userId,
        propertyId: data.propertyId,
        complaintTypeId: data.complaintTypeId,
        detail: data.detail,
        status: data.status,
        priority: data.priority,
      },
      include: {
        user: true,
        property: true,
        complaintType: true,
      },
    });
  }

  // 🔹 Obtener todas las quejas
  async findAllComplaints() {
    return this.prisma.complaint.findMany({
      include: {
        images: true,
        user: true,
        property: true,
        complaintType: true,
      },
    });
  }

  // 🔹 Obtener una queja por ID
  async getComplaintById(complaintId: string) {
    return this.prisma.complaint.findUnique({
      where: { id: complaintId },
      include: {
        images: true,
        user: true,
        property: true,
        complaintType: true,
      },
    });
  }

  // 🔹 Actualizar una queja
  async updateComplaint(complaintId: string, data: UpdateComplaintDto) {
    return this.prisma.complaint.update({
      where: { id: complaintId },
      data: {
        userId: data.userId,
        propertyId: data.propertyId,
        complaintTypeId: data.complaintTypeId,
        detail: data.detail,
        completedAt: data.completedAt,
        status: data.status,
        priority: data.priority,
      },
    });
  }

  // 🔹 Eliminar una queja
  async deleteComplaint(complaintId: string) {
    return this.prisma.complaint.delete({
      where: { id: complaintId },
    });
  }

  async findAllComplaintTypes() {
    return this.prisma.complaintType.findMany({});
  }

  async saveImagePath(complaintId: string, path: string) {
    return this.prisma.complaintImage.create({
      data: {
        complaintId,
        path,
      },
    });
  }

  async deleteAllImagesForReport(complaintId: string) {
    await this.prisma.complaintImage.deleteMany({
      where: { complaintId },
    });
  }

  async deleteImageById(imageId: string) {
    return this.prisma.complaintImage.delete({
      where: { id: imageId },
    });
  }

  async getComplaintsByUserId(userId: string) {
    return this.prisma.complaint.findMany({
      where: { userId },
      include: {
        images: true,
        user: true,
        property: true,
        complaintType: true,
      },
    });
  }
}
