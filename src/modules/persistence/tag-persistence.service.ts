import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreateTagDto } from '../business-logic/tag/dto/create-tag.dto';
import { UpdateTagDto } from '../business-logic/tag/dto/update-tag.dto';

@Injectable()
export class TagPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un tag
  async create(data: CreateTagDto) {
    return this.prisma.tag.create({
      data: {
        name: data.name,
        description: data.description || null,
        propertyId: data.propertyId,
        userId: data.userId || null,
      },
    });
  }

  // 🔹 Obtener todos los tags
  async findAll() {
    return this.prisma.tag.findMany();
  }

  // 🔹 Obtener un tag por ID
  async findById(tagId: string) {
    return this.prisma.tag.findUnique({
      where: { id: tagId },
    });
  }

  // 🔹 Actualizar un tag
  async update(tagId: string, data: UpdateTagDto) {
    return this.prisma.tag.update({
      where: { id: tagId },
      data,
    });
  }

  // 🔹 Eliminar un tag
  async delete(tagId: string) {
    return this.prisma.tag.delete({
      where: { id: tagId },
    });
  }
}
