import { Injectable } from '@nestjs/common';
import { CreateRentalDto } from '../business-logic/rental/dto/create-rental.dto';
import { UpdateRentalDto } from '../business-logic/rental/dto/update-rental.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class RentalPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un alquiler
  async create(data: CreateRentalDto) {
    return this.prisma.rental.create({
      data: {
        propertyId: data.propertyId,
        startDate: data.startDate,
        endDate: data.endDate || null,
        monthlyRate: data.monthlyRate,
        status: data.status,
      },
    });
  }

  // 🔹 Obtener todos los alquileres
  async findAll() {
    return this.prisma.rental.findMany({
      include: {
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Obtener un alquiler por ID
  async findById(rentalId: string) {
    return this.prisma.rental.findUnique({
      where: { id: rentalId },
      include: {
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Actualizar un alquiler
  async update(rentalId: string, data: UpdateRentalDto) {
    return this.prisma.rental.update({
      where: { id: rentalId },
      data,
    });
  }

  // 🔹 Eliminar un alquiler
  async delete(rentalId: string) {
    return this.prisma.rental.delete({
      where: { id: rentalId },
    });
  }
}
