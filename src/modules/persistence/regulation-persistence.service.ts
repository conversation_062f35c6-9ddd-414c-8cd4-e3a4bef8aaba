import { Injectable } from '@nestjs/common';
import { CreateRegulationDto } from '../business-logic/regulation/dto/create-regulation.dto';
import { UpdateRegulationDto } from '../business-logic/regulation/dto/update-regulation.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class RegulationPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un reglamento
  async create(data: CreateRegulationDto) {
    return this.prisma.regulation.create({
      data: {
        title: data.title,
        content: data.content,
        type: data.type,
        facilityId: data.facilityId || null,
        fileUrl: data.imageUrl,
      },
    });
  }

  // 🔹 Obtener todos los reglamentos
  async findAll() {
    return this.prisma.regulation.findMany({
      include: {
        facility: { select: { id: true, name: true } },
      },
    });
  }

  // 🔹 Obtener un reglamento por ID
  async findById(regulationId: string) {
    return this.prisma.regulation.findUnique({
      where: { id: regulationId },
      include: {
        facility: { select: { id: true, name: true } },
      },
    });
  }

  // 🔹 Actualizar un reglamento
  async update(regulationId: string, data: UpdateRegulationDto) {
    return this.prisma.regulation.update({
      where: { id: regulationId },
      data,
    });
  }

  // 🔹 Eliminar un reglamento
  async delete(regulationId: string) {
    return this.prisma.regulation.delete({
      where: { id: regulationId },
    });
  }
}
