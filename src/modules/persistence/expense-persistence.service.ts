import { Injectable } from '@nestjs/common';
import { CreateExpenseDto } from 'src/modules/business-logic/expense/dto/create-expense.dto';
import { UpdateExpenseDto } from 'src/modules/business-logic/expense/dto/update-expense.dto';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class ExpensePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un gasto
  async createExpense(data: CreateExpenseDto) {
    return this.prisma.expense.create({
      data: {
        description: data.description,
        amount: data.amount,
        date: data.date ?? new Date(),
        receipt: data.receipt,
      },
    });
  }

  // 🔹 Obtener todos los gastos
  async findAllExpenses() {
    return this.prisma.expense.findMany({});
  }

  // 🔹 Obtener un gasto por ID
  async getExpenseById(expenseId: string) {
    return this.prisma.expense.findUnique({
      where: { id: expenseId },
    });
  }

  // 🔹 Actualizar un gasto
  async updateExpense(expenseId: string, data: UpdateExpenseDto) {
    return this.prisma.expense.update({
      where: { id: expenseId },
      data,
    });
  }

  // 🔹 Eliminar un gasto
  async deleteExpense(expenseId: string) {
    return this.prisma.expense.delete({
      where: { id: expenseId },
    });
  }
}
