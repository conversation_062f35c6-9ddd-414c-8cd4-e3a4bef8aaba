import { Injectable } from '@nestjs/common';
import { Role } from '@prisma/client';
import { RoleSelectDto } from '../business-logic/role/dto/roles-select.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateRoleDto } from '../business-logic/role/dto/create-role.dto';
import { RoleResponseDto } from '../business-logic/role/dto/response-role.dto';
import { UpdateRoleDto } from '../business-logic/role/dto/update-role.dto';

@Injectable()
export class RolesPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createRoleDto: CreateRoleDto): Promise<RoleResponseDto> {
    try {
      return await this.prisma.role.create({
        data: createRoleDto,
      });
    } catch (error) {
      console.log(error);
    }
  }

  async findById(id: Role['id']): Promise<RoleResponseDto | null> {
    return await this.prisma.role.findUnique({
      where: { id },
    });
  }

  async findAll(): Promise<RoleResponseDto[]> {
    return await this.prisma.role.findMany({});
  }

  async findAllForSelect(): Promise<RoleSelectDto[]> {
    return await this.prisma.role.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  }

  async update(id: Role['id'], updateRoleDto: UpdateRoleDto): Promise<RoleResponseDto> {
    return await this.prisma.role.update({
      where: { id },
      data: updateRoleDto,
    });
  }

  async delete(id: string): Promise<RoleResponseDto> {
    return await this.prisma.role.delete({
      where: { id },
    });
  }
}
