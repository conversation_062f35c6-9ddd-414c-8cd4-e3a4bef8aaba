import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreateStepDto } from '../business-logic/step/dto/create-step.dto';
import { UpdateStepDto } from '../business-logic/step/dto/update-step.dto';

@Injectable()
export class StepPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un paso
  async create(data: CreateStepDto) {
    return this.prisma.step.create({
      data: {
        title: data.title,
        order: data.order,
        protocolId: data.protocolId,
      },
    });
  }

  // 🔹 Obtener todos los pasos
  async findAll() {
    return this.prisma.step.findMany();
  }

  // 🔹 Obtener un paso por ID
  async findById(stepId: string) {
    return this.prisma.step.findUnique({
      where: { id: stepId },
    });
  }

  // 🔹 Actualizar un paso
  async update(stepId: string, data: UpdateStepDto) {
    return this.prisma.step.update({
      where: { id: stepId },
      data,
    });
  }

  // 🔹 Eliminar un paso
  async delete(stepId: string) {
    return this.prisma.step.delete({
      where: { id: stepId },
    });
  }
}
