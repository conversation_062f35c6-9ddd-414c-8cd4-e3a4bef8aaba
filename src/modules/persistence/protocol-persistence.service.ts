import { Injectable } from '@nestjs/common';
import { CreateProtocolDto } from '../business-logic/protocol/dto/create-protocol.dto';
import { UpdateProtocolDto } from '../business-logic/protocol/dto/update-protocol.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class ProtocolPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un protocolo
  async create(data: CreateProtocolDto) {
    return this.prisma.protocol.create({
      data: {
        title: data.title,
        description: data.description,
        facilityId: data.facilityId || null,
        steps: {
          create:
            data.steps?.map((step) => ({
              title: step.title,
              order: step.order,
            })) || [],
        },
      },
      include: {
        steps: true,
      },
    });
  }

  // 🔹 Obtener todos los protocolos
  async findAll() {
    return this.prisma.protocol.findMany({
      include: {
        facility: { select: { id: true, name: true } },
        steps: true,
      },
    });
  }

  // 🔹 Obtener un protocolo por ID
  async findById(protocolId: string) {
    return this.prisma.protocol.findUnique({
      where: { id: protocolId },
      include: {
        facility: { select: { id: true, name: true } },
        steps: true,
      },
    });
  }

  // 🔹 Actualizar un protocolo
  async update(protocolId: string, data: UpdateProtocolDto) {
    await this.prisma.step.deleteMany({
      where: { protocolId },
    });

    // Luego actualizamos el protocolo y recreamos los pasos
    return this.prisma.protocol.update({
      where: { id: protocolId },
      data: {
        title: data.title,
        description: data.description,
        facilityId: data.facilityId || null,
        steps: {
          create:
            data.steps?.map((step) => ({
              title: step.title,
              order: step.order,
            })) || [],
        },
      },
      include: {
        steps: true,
      },
    });
  }

  // 🔹 Eliminar un protocolo
  async delete(protocolId: string) {
    return this.prisma.protocol.delete({
      where: { id: protocolId },
    });
  }
}
