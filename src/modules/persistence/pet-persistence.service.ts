import { Injectable } from '@nestjs/common';
import { CreatePetDto } from '../business-logic/pet/dto/create-pet.dto';
import { UpdatePetDto } from '../business-logic/pet/dto/update-pet.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class PetPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una mascota
  async createPet(data: CreatePetDto) {
    return this.prisma.pet.create({
      data: {
        name: data.name,
        type: data.type,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Obtener todas las mascotas
  async findAllPets() {
    return this.prisma.pet.findMany({
      include: {
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Obtener una mascota por ID
  async getPetById(petId: string) {
    return this.prisma.pet.findUnique({
      where: { id: petId },
      include: {
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Actualizar una mascota
  async updatePet(petId: string, data: UpdatePetDto) {
    return this.prisma.pet.update({
      where: { id: petId },
      data,
    });
  }

  // 🔹 Eliminar una mascota
  async deletePet(petId: string) {
    return this.prisma.pet.delete({
      where: { id: petId },
    });
  }
}
