import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreateSupplierDto } from '../business-logic/supplier/dto/create-supplier.dto';
import { SupplierSelectDto } from '../business-logic/supplier/dto/supplier-select.dto';
import { UpdateSupplierDto } from '../business-logic/supplier/dto/update-supplier.dto';

@Injectable()
export class SupplierPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un proveedor
  async create(data: CreateSupplierDto) {
    return this.prisma.supplier.create({
      data: {
        name: data.name,
        phone: data.phone || null,
        email: data.email || null,
        address: data.address || null,
      },
    });
  }

  // 🔹 Obtener todos los proveedores
  async findAll() {
    return this.prisma.supplier.findMany();
  }

  async findAllForSelect(): Promise<SupplierSelectDto[]> {
    return await this.prisma.supplier.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  }

  // 🔹 Obtener un proveedor por ID
  async findById(supplierId: string) {
    return this.prisma.supplier.findUnique({
      where: { id: supplierId },
    });
  }

  // 🔹 Actualizar un proveedor
  async update(supplierId: string, data: UpdateSupplierDto) {
    return this.prisma.supplier.update({
      where: { id: supplierId },
      data,
    });
  }

  // 🔹 Eliminar un proveedor
  async delete(supplierId: string) {
    return this.prisma.supplier.delete({
      where: { id: supplierId },
    });
  }
}
