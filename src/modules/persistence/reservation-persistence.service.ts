import { Injectable } from '@nestjs/common';
import { CreateReservationDto } from '../business-logic/reservation/dto/create-reservation.dto';
import { UpdateReservationDto } from '../business-logic/reservation/dto/update-reservation.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { DenyReservationDto } from '../business-logic/reservation/dto/deny-reservation.dto';
import { ReservationStatus } from '@prisma/client';

@Injectable()
export class ReservationPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear una reserva
  async create(data: CreateReservationDto) {
    return this.prisma.reservation.create({
      data: {
        propertyId: data.propertyId,
        facilityId: data.facilityId,
        amountOfPeople: data.amountOfPeople,
        startDateTime: data.startDateTime,
        endDateTime: data.endDateTime,
        status: ReservationStatus.PENDING,
        requestedBy: data.requestedBy,
        authorizedBy: data.authorizedBy,
        deniedBy: data.deniedBy,
        deniedReason: data.deniedReason,
      },
    });
  }

  // 🔹 Obtener todas las reservas
  async findAll() {
    return this.prisma.reservation.findMany({
      include: {
        property: { select: { id: true, address: true } },
        facility: { select: { id: true, name: true } },
      },
    });
  }

  // 🔹 Obtener una reserva por ID
  async findById(reservationId: string) {
    return this.prisma.reservation.findUnique({
      where: { id: reservationId },
      include: {
        property: { select: { id: true, address: true } },
        facility: { select: { id: true, name: true } },
      },
    });
  }

  // 🔹 Actualizar una reserva
  async update(reservationId: string, data: UpdateReservationDto) {
    return this.prisma.reservation.update({
      where: { id: reservationId },
      data,
    });
  }

  // 🔹 Eliminar una reserva
  async delete(reservationId: string) {
    return this.prisma.reservation.delete({
      where: { id: reservationId },
    });
  }

  async authorize(id: string, userId: string) {
    return this.prisma.reservation.update({
      where: { id },
      data: {
        status: 'APPROVED',
        authorizedBy: userId,
        authorizedAt: new Date(),
        deniedBy: null,
        deniedAt: null,
        deniedReason: null,
      },
    });
  }

  async deny(id: string, dto: DenyReservationDto, userid: string) {
    return this.prisma.reservation.update({
      where: { id },
      data: {
        status: 'REJECTED',
        deniedBy: userid,
        deniedAt: new Date(),
        deniedReason: dto.deniedReason,
        authorizedBy: null,
        authorizedAt: null,
      },
    });
  }

  async getReservationsByUserId(userId: string) {
    return this.prisma.reservation.findMany({
      where: { requestedBy: userId },
      include: {
        property: { select: { id: true, address: true } },
        facility: { select: { id: true, name: true } },
      },
    });
  }

  async findFacilityReservations(facilityId: string) {
    return this.prisma.reservation.findMany({
      where: {
        facilityId,
        startDateTime: {
          gte: new Date(),
          lte: new Date(new Date().setMonth(new Date().getMonth() + 1)),
        },
      },
      include: {
        property: { select: { id: true, address: true } },
        facility: { select: { id: true, name: true } },
      },
    });
  }
}
