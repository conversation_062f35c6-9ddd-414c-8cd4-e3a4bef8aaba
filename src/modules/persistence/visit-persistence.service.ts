import { Injectable } from '@nestjs/common';
import { CreateVisitDto } from 'src/modules/business-logic/visit/dto/create-visit.dto';
import { PrismaService } from '../database/prisma/prisma.service';
import { VisitResponseDto } from '../business-logic/visit/dto/visit-response-dto';

@Injectable()
export class VisitPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async createVisit(data: CreateVisitDto) {
    const visit = await this.prisma.visit.create({
      data: {
        propertyId: data.propertyId,
        visitorName: data.visitorName,
        visitMethod: data.visitMethod,
        vehiclePlate: data.vehiclePlate ?? null,
        schedule: data.schedule ? new Date(data.schedule) : new Date(),
        checkInTime: data.checkInTime ? new Date(data.checkInTime) : new Date(),
        parkingSpotId: data.parkingSpotId ?? null,
        qrCode: this.generateQRCode(),
        requestedBy: data.requestedBy,
      },
    });

    if (data.parkingSpotId) {
      await this.prisma.parkingSpot.update({
        where: { id: data.parkingSpotId },
        data: { isAvailable: false },
      });
    }

    return visit;
  }

  async findAll(): Promise<VisitResponseDto[]> {
    return this.prisma.visit.findMany({
      include: {
        property: true,
        parkingSpot: true,
      },
    });
  }

  async getById(visitId: string) {
    return this.prisma.visit.findUnique({
      where: { id: visitId },
      include: {
        property: true,
        parkingSpot: true,
      },
    });
  }

  async update(visitId: string, data: Partial<CreateVisitDto>) {
    return this.prisma.visit.update({
      where: { id: visitId },
      data,
    });
  }

  async delete(visitId: string) {
    const visit = await this.prisma.visit.findUnique({
      where: { id: visitId },
    });

    if (!visit) throw new Error('Visita no encontrada.');

    if (visit.parkingSpotId) {
      await this.prisma.parkingSpot.update({
        where: { id: visit.parkingSpotId },
        data: { isAvailable: true },
      });
    }

    return this.prisma.visit.delete({
      where: { id: visitId },
    });
  }

  async checkOutVisit(visitId: string) {
    const visit = await this.prisma.visit.findUnique({
      where: { id: visitId },
    });

    if (!visit) throw new Error('Visita no encontrada.');

    await this.prisma.visit.update({
      where: { id: visitId },
      data: { checkOutTime: new Date() },
    });

    if (visit.parkingSpotId) {
      await this.prisma.parkingSpot.update({
        where: { id: visit.parkingSpotId },
        data: { isAvailable: true },
      });
    }

    return { message: 'Visita finalizada correctamente.' };
  }

  async generateVisitorQR(visitorName: string) {
    return {
      qrCode: this.generateQRCode(),
      message: `QR generado para ${visitorName}`,
    };
  }

  private generateQRCode(): string {
    return `QR-${Math.random().toString(36).slice(2, 11)}`;
  }
}
