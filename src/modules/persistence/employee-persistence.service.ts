import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateEmployeeDto } from '../business-logic/employee/dto/create-employee.dto';
import { UpdateEmployeeDto } from '../business-logic/employee/dto/update-employee.dto';

@Injectable()
export class EmployeePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un empleado
  async createEmployee(data: CreateEmployeeDto) {
    return this.prisma.employee.create({
      data,
    });
  }

  // 🔹 Obtener todos los empleados
  async findAllEmployees() {
    return this.prisma.employee.findMany({
      include: { supplier: true }, // Incluye proveedor si existe
    });
  }

  // 🔹 Obtener un empleado por ID
  async getEmployeeById(employeeId: string) {
    return this.prisma.employee.findUnique({
      where: { id: employeeId },
      include: { supplier: true },
    });
  }

  // 🔹 Actualizar un empleado
  async updateEmployee(employeeId: string, data: UpdateEmployeeDto) {
    return this.prisma.employee.update({
      where: { id: employeeId },
      data,
    });
  }

  // 🔹 Eliminar un empleado
  async deleteEmployee(employeeId: string) {
    return this.prisma.employee.delete({
      where: { id: employeeId },
    });
  }
}
