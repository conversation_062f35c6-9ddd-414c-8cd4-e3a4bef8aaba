import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { UpdateMaintenanceIssueReportDto } from '../business-logic/maintenance-issue-report/dto/update-maintenance-issue-report.dto';
import { CreateMaintenanceIssueReportDto } from '../business-logic/maintenance-issue-report/dto/create-maintenance-issue-report.dto';

@Injectable()
export class MaintenanceIssueReportPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un reporte de problema en instalaciones
  async createMaintenanceIssueReport(data: CreateMaintenanceIssueReportDto) {
    return this.prisma.maintenanceIssueReport.create({
      data,
    });
  }

  // 🔹 Obtener todos los reportes
  async findAllMaintenanceIssueReports() {
    return this.prisma.maintenanceIssueReport.findMany({
      include: {
        images: true,
        user: { select: { id: true, firstName: true, email: true } },
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Obtener un reporte por ID
  async getMaintenanceIssueReportById(reportId: string) {
    return this.prisma.maintenanceIssueReport.findUnique({
      where: { id: reportId },
      include: {
        images: true,
        user: { select: { id: true, firstName: true, email: true } },
        property: { select: { id: true, address: true } },
      },
    });
  }

  // 🔹 Actualizar un reporte
  async updateMaintenanceIssueReport(reportId: string, data: UpdateMaintenanceIssueReportDto) {
    return this.prisma.maintenanceIssueReport.update({
      where: { id: reportId },
      data: {
        description: data.description,
        status: data.status,
        reportedBy: data.reportedBy,
        propertyId: data.propertyId,
      },
    });
  }

  // 🔹 Eliminar un reporte
  async deleteMaintenanceIssueReport(reportId: string) {
    return this.prisma.maintenanceIssueReport.delete({
      where: { id: reportId },
    });
  }

  async saveImagePath(reportId: string, path: string) {
    return this.prisma.maintenanceIssueImage.create({
      data: {
        reportId,
        path,
      },
    });
  }

  async deleteAllImagesForReport(reportId: string) {
    await this.prisma.maintenanceIssueImage.deleteMany({
      where: { reportId },
    });
  }

  async deleteImageById(imageId: string) {
    return this.prisma.maintenanceIssueImage.delete({
      where: { id: imageId },
    });
  }

  async getMaintenanceIssueReportsByUserId(userId: string) {
    return this.prisma.maintenanceIssueReport.findMany({
      where: { reportedBy: userId },
      include: {
        images: true,
        user: { select: { id: true, firstName: true, email: true } },
        property: { select: { id: true, address: true } },
      },
    });
  }
}
