import { Injectable } from '@nestjs/common';
import { ParkingSpot, ParkingSpotType } from '@prisma/client';
import { ParkingSpotSelectDto } from '../business-logic/parking-spot/dto/parking-spot-select.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateParkingSpotDto } from '../business-logic/parking-spot/dto/create-parking-spot.dto';
import { ParkingSpotResponseDto } from '../business-logic/parking-spot/dto/response-parking-spot.dto';
import { UpdateParkingSpotDto } from '../business-logic/parking-spot/dto/update-parking-spot.dto';

@Injectable()
export class ParkingSpotPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createParkingSpotDto: CreateParkingSpotDto): Promise<ParkingSpotResponseDto> {
    try {
      return await this.prisma.parkingSpot.create({
        data: createParkingSpotDto,
      });
    } catch (error) {
      console.log(error);
    }
  }

  async findById(id: ParkingSpot['id']): Promise<ParkingSpotResponseDto | null> {
    return await this.prisma.parkingSpot.findUnique({
      where: { id },
    });
  }

  async findAll(): Promise<ParkingSpotResponseDto[]> {
    return await this.prisma.parkingSpot.findMany({});
  }

  async findAllForSelect(): Promise<ParkingSpotSelectDto[]> {
    return await this.prisma.parkingSpot.findMany({
      select: {
        id: true,
        spotNumber: true,
      },
    });
  }

  async findVisitors(): Promise<ParkingSpotResponseDto[]> {
    const availableSpots = await this.prisma.parkingSpot.findMany({
      where: { type: ParkingSpotType.VISITOR },
    });

    return availableSpots || [];
  }

  async findByProperty(propertyId: ParkingSpot['propertyId']): Promise<ParkingSpotResponseDto[]> {
    const availableSpots = await this.prisma.parkingSpot.findMany({
      where: { propertyId },
    });

    return availableSpots || [];
  }

  async update(id: ParkingSpot['id'], updateParkingSpotDto: UpdateParkingSpotDto): Promise<ParkingSpotResponseDto> {
    return await this.prisma.parkingSpot.update({
      where: { id },
      data: updateParkingSpotDto,
    });
  }

  async delete(id: string): Promise<ParkingSpotResponseDto> {
    return await this.prisma.parkingSpot.delete({
      where: { id },
    });
  }
}
