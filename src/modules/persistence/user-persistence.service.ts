import { Injectable } from '@nestjs/common';
import { User } from '@prisma/client';
import { CreateUserWithTokenDto } from 'src/modules/business-logic/user/dto/create-user-with-token.dto';
import { UserResponseDto } from 'src/modules/business-logic/user/dto/user-response.dto';
import { UpdateUserDto } from 'src/modules/business-logic/user/dto/update-user.dto';
import { PrismaService } from '../database/prisma/prisma.service';
import { UserSelectDto } from '../business-logic/user/dto/user-select.dto';
import { UserWithRoles } from '../auth/auth.types';
import { MobileUserResponseDto } from '../mobile/dto/mobile-user-response.dto';

@Injectable()
export class UserPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async createUser(createUserDto: CreateUserWithTokenDto): Promise<UserResponseDto> {
    try {
      return await this.prisma.user.create({
        data: {
          ...createUserDto,
          roles: {
            connect: createUserDto.roles.map((roleId) => ({ id: roleId })),
          },
          properties: {
            connect: createUserDto.properties.map((propertyId) => ({ id: propertyId })),
          },
        },
      });
    } catch (error) {
      console.log(error);
    }
  }

  async findById(id: User['id']): Promise<UserResponseDto | null> {
    return await this.prisma.user.findUnique({
      where: { id },
      include: {
        announcementRecipient: true,
        complaints: true,
        maintenanceIssueReports: true,
        newsItems: true,
        properties: true,
        roles: true,
        tags: true,
        tasks: true,
        property: true,
      },
    });
  }

  async findAll(): Promise<UserResponseDto[]> {
    return await this.prisma.user.findMany({
      where: { isDeleted: false },
      include: { roles: true, properties: true },
    });
  }

  async findAllForSelect(): Promise<UserSelectDto[]> {
    return await this.prisma.user.findMany({
      select: {
        id: true,
        firstName: true,
        paternalLastName: true,
        maternalLastName: true,
      },
    });
  }

  async updateUser(id: User['id'], updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    const { roles, ...rest } = updateUserDto;

    return await this.prisma.user.update({
      where: { id },
      data: {
        ...rest, // Incluye el resto de los campos
        roles: roles
          ? {
              connect: roles.map((roleId) => ({ id: roleId })), // Conecta los roles por ID
            }
          : undefined, // Si no hay roles, no intentes actualizar
        properties: roles
          ? {
              connect: updateUserDto.properties.map((propertyId) => ({ id: propertyId })), // Conecta los roles por ID
            }
          : undefined, // Si no hay roles, no intentes actualizar
      },
    });
  }

  async softDelete(id: User['id']): Promise<UserResponseDto> {
    return await this.prisma.user.update({
      where: { id },
      data: { isDeleted: true },
    });
  }

  async delete(id: string): Promise<UserResponseDto> {
    return await this.prisma.user.delete({
      where: { id },
    });
  }

  async findByEmail(email: User['email']): Promise<UserWithRoles> {
    return await this.prisma.user.findUnique({
      where: { email },
      include: {
        roles: true,
      },
    });
  }

  async findByPasswordConfirmationToken(token: string) {
    return await this.prisma.user.findUnique({
      where: { passwordConfirmationToken: token },
    });
  }

  async savePassword(id: User['id'], password: User['password']) {
    return await this.prisma.user.update({
      where: { id },
      data: {
        password,
        passwordConfirmationToken: null,
        passwordConfirmed: true,
      },
    });
  }

  async findForMobileById(id: User['id']): Promise<MobileUserResponseDto | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        paternalLastName: true,
        maternalLastName: true,
        phone: true,
        properties: {
          select: {
            id: true,
            address: true,
            type: true,
          },
        },
      },
    });

    const announcements = await this.prisma.announcement.findMany({
      where: {
        role: {
          users: {
            some: {
              id,
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
      select: {
        id: true,
        title: true,
        createdAt: true,
        message: true,
        imageUrl: true,
        images: true,
        role: {
          select: {
            name: true,
          },
        },
      },
    });
    return { ...user, announcements };
  }
}
