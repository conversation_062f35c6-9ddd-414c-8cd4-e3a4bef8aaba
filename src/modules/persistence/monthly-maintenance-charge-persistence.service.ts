// src/monthly-maintenance-charge/monthly-maintenance-charge-persistence.service.ts

import { Injectable } from '@nestjs/common';
import { CreateMonthlyMaintenanceChargeDto } from '../business-logic/monthly-maintenance-charge/dto/create-monthly-maintenance-charge.dto';
import { UpdateMonthlyMaintenanceChargeDto } from '../business-logic/monthly-maintenance-charge/dto/uptate-monthly-maintenance-charge.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';

@Injectable()
export class MonthlyMaintenanceChargePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateMonthlyMaintenanceChargeDto) {
    return this.prisma.monthlyMaintenanceCharge.create({
      data,
    });
  }

  async findAll(data?: { year?: number; month?: number }) {
    return this.prisma.monthlyMaintenanceCharge.findMany({
      where: {
        year: data.year,
        month: data.month,
      },
      include: {
        property: true,
        maintenanceFee: true,
        payment: true,
      },
    });
  }

  async findById(id: string) {
    return this.prisma.monthlyMaintenanceCharge.findUnique({
      where: { id },
      include: {
        property: true,
        maintenanceFee: true,
        payment: true,
      },
    });
  }

  async update(id: string, data: UpdateMonthlyMaintenanceChargeDto) {
    return this.prisma.monthlyMaintenanceCharge.update({
      where: { id },
      data,
    });
  }

  async delete(id: string) {
    return this.prisma.monthlyMaintenanceCharge.delete({
      where: { id },
    });
  }

  async markAsPaid(id: string) {
    return this.prisma.monthlyMaintenanceCharge.update({
      where: { id },
      data: {
        isPaid: true,
        paidAt: new Date(),
      },
    });
  }
}
