import { Injectable } from '@nestjs/common';
import { ComplaintTypeSelectDto } from '../business-logic/complaint-type/dto/complaint-type-select.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateComplaintTypeDto } from '../business-logic/complaint-type/dto/create-complaint-type.dto';
import { UpdateComplaintTypeDto } from '../business-logic/complaint-type/dto/update-complaint-type.dto';

@Injectable()
export class ComplaintTypePersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un tipo de queja
  async createComplaintType(data: CreateComplaintTypeDto) {
    return this.prisma.complaintType.create({
      data,
    });
  }

  // 🔹 Obtener todos los tipos de quejas
  async findAllComplaintTypes() {
    return this.prisma.complaintType.findMany();
  }

  async findAllForSelect(): Promise<ComplaintTypeSelectDto[]> {
    return await this.prisma.complaintType.findMany({
      select: {
        id: true,
        name: true,
      },
    });
  }

  // 🔹 Obtener un tipo de queja por ID
  async getComplaintTypeById(complaintTypeId: string) {
    return this.prisma.complaintType.findUnique({
      where: { id: complaintTypeId },
    });
  }

  // 🔹 Actualizar un tipo de queja
  async updateComplaintType(complaintTypeId: string, data: UpdateComplaintTypeDto) {
    return this.prisma.complaintType.update({
      where: { id: complaintTypeId },
      data,
    });
  }

  // 🔹 Eliminar un tipo de queja
  async deleteComplaintType(complaintTypeId: string) {
    return this.prisma.complaintType.delete({
      where: { id: complaintTypeId },
    });
  }
}
