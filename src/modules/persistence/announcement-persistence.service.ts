import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreateAnnouncementDto } from '../business-logic/announcement/dto/create-announcement.dto';
import { UpdateAnnouncementDto } from '../business-logic/announcement/dto/update-announcement.dto';

@Injectable()
export class AnnouncementPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un anuncio y asignar destinatarios
  async createAnnouncement(data: CreateAnnouncementDto) {
    return this.prisma.announcement.create({
      data: {
        title: data.title,
        message: data.message,
        roleId: data.roleId,
        recipients: data.recipients?.length
          ? {
              create: data.recipients.map((userId) => ({
                user: { connect: { id: userId } },
              })),
            }
          : undefined,
      },
      include: { recipients: true },
    });
  }

  // 🔹 Obtener todos los anuncios con destinatarios
  async findAllAnnouncements() {
    return this.prisma.announcement.findMany({
      include: {
        images: true,
        recipients: {
          select: {
            user: {
              select: { id: true, firstName: true, email: true }, // 🔥 Obtiene info del usuario
            },
            readAt: true, // Muestra si el usuario ha leído el anuncio
          },
        },
      },
    });
  }

  // 🔹 Obtener un anuncio por ID con destinatarios
  async getAnnouncementById(announcementId: string) {
    return this.prisma.announcement.findUnique({
      where: { id: announcementId },
      include: {
        images: true,
        recipients: {
          select: {
            user: {
              select: { id: true, firstName: true, email: true },
            },
            readAt: true,
          },
        },
      },
    });
  }

  // 🔹 Actualizar un anuncio (sin afectar destinatarios)
  async updateAnnouncement(announcementId: string, data: UpdateAnnouncementDto) {
    return this.prisma.announcement.update({
      where: { id: announcementId },
      data: {
        title: data.title,
        message: data.message,
        roleId: data.roleId,
        recipients: data.recipients?.length
          ? {
              set: [], // 🔥 Limpia los destinatarios previos
              create: data.recipients.map((userId) => ({
                user: { connect: { id: userId } },
              })),
            }
          : undefined,
      },
      include: { recipients: true },
    });
  }

  // 🔹 Eliminar un anuncio (Elimina también `AnnouncementRecipient` por `onDelete: Cascade`)
  async deleteAnnouncement(announcementId: string) {
    return this.prisma.announcement.delete({
      where: { id: announcementId },
    });
  }

  // 🔹 Marcar un anuncio como leído por un usuario
  async markAsRead(announcementId: string, userId: string) {
    return this.prisma.announcementRecipient.update({
      where: { userId_announcementId: { userId, announcementId } },
      data: { readAt: new Date() },
    });
  }

  async saveImagePath(announcementId: string, path: string) {
    return this.prisma.announcementImage.create({
      data: {
        announcementId,
        path,
      },
    });
  }

  async deleteAllImageForAnnouncement(announcementId: string) {
    await this.prisma.announcementImage.deleteMany({
      where: { announcementId },
    });
  }

  async deleteImageById(imageId: string) {
    return this.prisma.announcementImage.delete({
      where: { id: imageId },
    });
  }
}
