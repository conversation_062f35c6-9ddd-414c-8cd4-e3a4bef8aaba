import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreatePhoneDirectoryDto } from '../business-logic/phone-directory/dto/create-phone-directory.dto';
import { UpdatePhoneDirectoryDto } from '../business-logic/phone-directory/dto/update-phone-directory.dto';

@Injectable()
export class PhoneDirectoryPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un contacto en el directorio telefónico
  async create(data: CreatePhoneDirectoryDto) {
    return this.prisma.phoneDirectory.create({
      data: {
        name: data.name,
        phoneNumber: data.phoneNumber,
      },
    });
  }

  // 🔹 Obtener todos los contactos del directorio
  async findAll() {
    return this.prisma.phoneDirectory.findMany({
      orderBy: {
        name: 'asc',
      },
    });
  }

  // 🔹 Obtener un contacto por ID
  async findById(phoneDirectoryId: string) {
    return this.prisma.phoneDirectory.findUnique({
      where: { id: phoneDirectoryId },
    });
  }

  // 🔹 Actualizar un contacto
  async update(phoneDirectoryId: string, data: UpdatePhoneDirectoryDto) {
    return this.prisma.phoneDirectory.update({
      where: { id: phoneDirectoryId },
      data,
    });
  }

  // 🔹 Eliminar un contacto
  async delete(phoneDirectoryId: string) {
    return this.prisma.phoneDirectory.delete({
      where: { id: phoneDirectoryId },
    });
  }
}
