import { Inject, Injectable } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import * as admin from 'firebase-admin';
import configuration from 'src/config/configuration';

@Injectable()
export class PushService {
  constructor(
    @Inject(configuration.KEY)
    private readonly config: ConfigType<typeof configuration>,
  ) {
    // Clave privada JSON
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: this.config.firebase.projectId,
        clientEmail: this.config.firebase.clientEmail,
        privateKey: this.config.firebase.privateKey?.replace(/\\n/g, '\n'),
      }),
    });
  }

  async sendPushNotification(
    token: string, // Token de dispositivo único
    title: string,
    body: string,
  ) {
    const message = {
      notification: {
        title,
        body,
      },
      token,
    };
    return admin.messaging().send(message);
  }
}
