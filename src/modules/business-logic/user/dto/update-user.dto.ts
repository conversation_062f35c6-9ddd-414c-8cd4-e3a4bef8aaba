import { PartialType } from '@nestjs/mapped-types';
import { CreateUserDto } from './create-user.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsBoolean, IsUUID } from 'class-validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty({
    description: 'Estado del usuario (activo o eliminado)',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isDeleted?: boolean;

  @ApiProperty({
    description: 'Lista de roles asignados al usuario',
    type: [String],
    example: ['admin-uuid', 'user-uuid'],
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { each: true })
  roles?: string[];

  @ApiProperty({
    description: 'Lista de propiedades asociadas al usuario',
    type: [String],
    example: ['property-uuid1', 'property-uuid2'],
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { each: true })
  properties?: string[];
}
