import { ApiProperty } from '@nestjs/swagger';
import { User } from '@prisma/client';

export interface UserResponse extends Omit<User, 'password' | 'passwordConfirmationToken' | 'isDeleted'> {}

export class UserResponseDto implements UserResponse {
  @ApiProperty({ description: 'Id del usuario', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  id: string;

  @ApiProperty({ description: 'Email del usuario', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ description: 'Nombre del usuario', example: 'Juan' })
  firstName: string;

  @ApiProperty({ description: 'Apellido paterno del usuario', example: 'Pérez' })
  paternalLastName: string;

  @ApiProperty({ description: 'Apellido materno del usuario', example: 'Pérez' })
  maternalLastName: string;

  @ApiProperty({ description: 'Teléfono del usuario', example: '5544332211' })
  phone: string;

  @ApiProperty({ description: 'Fecha de creación', example: '2024-11-15T05:00:14.855Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Fecha de actualización', example: '2024-11-15T05:00:14.855Z' })
  updatedAt: Date;

  @ApiProperty({ description: 'El password está configurado', example: false })
  passwordConfirmed: boolean;
}
