import { ApiProperty } from '@nestjs/swagger';
import { Property, Role, User } from '@prisma/client';
import { IsArray, IsEmail, IsNotEmpty, IsString } from 'class-validator';

export interface CreateUserRequest
  extends Omit<
    User,
    'id' | 'password' | 'passwordConfirmed' | 'passwordConfirmationToken' | 'isDeleted' | 'createdAt' | 'updatedAt'
  > {}

export class CreateUserDto implements CreateUserRequest {
  @ApiProperty({ description: 'Email del usuario', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Nombre del usuario', example: 'Juan' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'Apellido paterno del usuario', example: 'Pérez' })
  @IsString()
  @IsNotEmpty()
  paternalLastName: string;

  @ApiProperty({ description: 'Apellido materno del usuario', example: 'Pérez' })
  @IsString()
  @IsNotEmpty()
  maternalLastName: string;

  @ApiProperty({ description: 'Teléfono del usuario', example: '5544332211' })
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ description: 'Roles asignados al usuario', example: '98y4309582740958' })
  @IsNotEmpty()
  @IsArray()
  roles: Role['id'][];

  @ApiProperty({ description: 'Propiedades asignadas al usuario', example: '98y4309582740958' })
  @IsNotEmpty()
  @IsArray()
  properties: Property['id'][];
}
