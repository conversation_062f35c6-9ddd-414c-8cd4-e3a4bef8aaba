import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { MailModule } from '../../mail/mail.module';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';

@Module({
  imports: [PersistenceModule, MailModule],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {}
