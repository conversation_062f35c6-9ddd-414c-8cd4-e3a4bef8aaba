import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  VERSION_NEUTRAL,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from '@prisma/client';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { internalServerErrorResponseDoc, porviderExceptionErrorResponseDoc } from 'src/docs/api-server-response.doc';
import { UserResponseDto } from './dto/user-response.dto';

@ApiTags('User')
@ApiBadRequestResponse(porviderExceptionErrorResponseDoc)
@ApiInternalServerErrorResponse(internalServerErrorResponseDoc)
@Controller({ path: 'user', version: ['1', VERSION_NEUTRAL] })
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: 'Create a user', description: 'Create user' })
  @ApiResponse({ type: UserResponseDto })
  @ApiBody({ type: CreateUserDto })
  async create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
    return await this.userService.create(createUserDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all users', description: 'Get all users' })
  @ApiResponse({ type: UserResponseDto })
  async findAll(): Promise<UserResponseDto[]> {
    return this.userService.findAll();
  }

  @Get('for-select')
  findAllForSelect() {
    return this.userService.findAllForSelect();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a user', description: 'Get user' })
  @ApiResponse({ type: UserResponseDto })
  @ApiParam({ name: 'id', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f', type: String })
  async findOne(@Param('id', new ParseUUIDPipe()) id: User['id']) {
    return this.userService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a user', description: 'Update user' })
  @ApiResponse({ type: UserResponseDto })
  @ApiParam({ name: 'id', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f', type: String })
  async update(@Param('id', new ParseUUIDPipe()) id: User['id'], @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(id, updateUserDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user', description: 'Soft delete by default, hard delete with ?force=true.' })
  @ApiParam({ name: 'id', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f', type: String })
  @ApiQuery({
    name: 'force',
    description: 'Set to true for hard delete',
    required: false,
    type: Boolean,
  })
  @ApiResponse({ type: UserResponseDto })
  async delete(@Param('id', new ParseUUIDPipe()) id: User['id'], @Query('force') force?: string) {
    return force === 'true' ? this.userService.hardDelete(id) : this.userService.softDelete(id);
  }
}
