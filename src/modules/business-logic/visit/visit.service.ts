import { Injectable } from '@nestjs/common';
import { CreateVisitDto } from './dto/create-visit.dto';
import { VisitPersistenceService } from 'src/modules/persistence/visit-persistence.service';

@Injectable()
export class VisitService {
  constructor(private readonly visitPersistence: VisitPersistenceService) {}

  async createVisit(data: CreateVisitDto) {
    return this.visitPersistence.createVisit(data);
  }

  async findAll() {
    return this.visitPersistence.findAll();
  }

  async getById(visitId: string) {
    return this.visitPersistence.getById(visitId);
  }

  async update(visitId: string, data: Partial<CreateVisitDto>) {
    return this.visitPersistence.update(visitId, data);
  }

  async delete(visitId: string) {
    return this.visitPersistence.delete(visitId);
  }

  async checkOutVisit(visitId: string) {
    return this.visitPersistence.checkOutVisit(visitId);
  }

  async generateVisitorQR(visitorName: string) {
    return this.visitPersistence.generateVisitorQR(visitorName);
  }
}
