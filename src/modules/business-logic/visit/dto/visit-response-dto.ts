import { ApiProperty } from '@nestjs/swagger';

export class VisitResponseDto {
  @ApiProperty({ description: 'Id de la visita', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  id: string;

  @ApiProperty({ description: 'Número de estacionamiento', example: 'EVP-1' })
  propertyId: string;

  @ApiProperty({ description: 'Nombre del visitante', example: 'Juan <PERSON>' })
  visitorName: string;

  @ApiProperty({ description: 'Placa del vehículo', example: 'JK34KH' })
  vehiclePlate?: string;

  @ApiProperty({ description: 'Id del lugar de estacionamiento', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  parkingSpotId?: string;

  @ApiProperty({ description: 'Fecha de creación', example: '2024-11-15T05:00:14.855Z' })
  schedule: Date;

  @ApiProperty({ description: '<PERSON>cha de creación', example: '2024-11-15T05:00:14.855Z' })
  checkInTime?: Date;

  @ApiProperty({ description: 'Fecha de creación', example: '2024-11-15T05:00:14.855Z' })
  checkOutTime?: Date;
}
