import { <PERSON>, Post, Patch, Get, Delete, Body, Param, ParseUUIDPipe } from '@nestjs/common';
import { VisitService } from './visit.service';
import { CreateVisitDto } from './dto/create-visit.dto';
import { GenerateQrDto } from './dto/generate-qr.dto';

@Controller('visit')
export class VisitController {
  constructor(private readonly visitService: VisitService) {}

  @Post()
  async createVisit(@Body() createVisitDto: CreateVisitDto) {
    return this.visitService.createVisit(createVisitDto);
  }

  @Get()
  async findAll() {
    return this.visitService.findAll();
  }

  @Get('/:visitId')
  async getById(@Param('visitId', ParseUUIDPipe) visitId: string) {
    return this.visitService.getById(visitId);
  }

  @Patch('/:visitId')
  async update(@Param('visitId', ParseUUIDPipe) visitId: string, @Body() updateData: Partial<CreateVisitDto>) {
    return this.visitService.update(visitId, updateData);
  }

  @Delete('/:visitId')
  async delete(@Param('visitId', ParseUUIDPipe) visitId: string) {
    return this.visitService.delete(visitId);
  }

  @Patch('/:visitId/checkout')
  async checkOutVisit(@Param('visitId', ParseUUIDPipe) visitId: string) {
    return this.visitService.checkOutVisit(visitId);
  }

  @Post('/generate-qr')
  async generateVisitorQR(@Body() generateQrDto: GenerateQrDto) {
    return this.visitService.generateVisitorQR(generateQrDto.visitorName);
  }
}
