import { Module } from '@nestjs/common';
import { ComplaintTypeService } from './complaint-type.service';
import { ComplaintTypeController } from './complaint-type.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';

@Module({
  imports: [PersistenceModule],
  controllers: [ComplaintTypeController],
  providers: [ComplaintTypeService],
  exports: [ComplaintTypeService],
})
export class ComplaintTypeModule {}
