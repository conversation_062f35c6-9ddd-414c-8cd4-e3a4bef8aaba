import { Injectable } from '@nestjs/common';
import { CreateComplaintTypeDto } from './dto/create-complaint-type.dto';
import { UpdateComplaintTypeDto } from './dto/update-complaint-type.dto';
import { ComplaintTypePersistenceService } from '../../persistence/complaint-type-persistence.service';

@Injectable()
export class ComplaintTypeService {
  constructor(private readonly complaintTypePersistence: ComplaintTypePersistenceService) {}

  async createComplaintType(data: CreateComplaintTypeDto) {
    return this.complaintTypePersistence.createComplaintType(data);
  }

  async findAllComplaintTypes() {
    return this.complaintTypePersistence.findAllComplaintTypes();
  }

  async findAllForSelect() {
    return await this.complaintTypePersistence.findAllForSelect();
  }

  async getComplaintTypeById(complaintTypeId: string) {
    return this.complaintTypePersistence.getComplaintTypeById(complaintTypeId);
  }

  async updateComplaintType(complaintTypeId: string, data: UpdateComplaintTypeDto) {
    return this.complaintTypePersistence.updateComplaintType(complaintTypeId, data);
  }

  async deleteComplaintType(complaintTypeId: string) {
    return this.complaintTypePersistence.deleteComplaintType(complaintTypeId);
  }
}
