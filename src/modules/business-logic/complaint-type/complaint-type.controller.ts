import { <PERSON>, Post, Get, Delete, Body, Param, ParseUUIDPipe, Patch } from '@nestjs/common';
import { ComplaintTypeService } from './complaint-type.service';
import { CreateComplaintTypeDto } from './dto/create-complaint-type.dto';
import { UpdateComplaintTypeDto } from './dto/update-complaint-type.dto';

@Controller('complaint-type')
export class ComplaintTypeController {
  constructor(private readonly complaintTypeService: ComplaintTypeService) {}

  @Post()
  async createComplaintType(@Body() createComplaintTypeDto: CreateComplaintTypeDto) {
    return this.complaintTypeService.createComplaintType(createComplaintTypeDto);
  }

  @Get()
  async findAllComplaintTypes() {
    return this.complaintTypeService.findAllComplaintTypes();
  }

  @Get('for-select')
  findAllForSelect() {
    return this.complaintTypeService.findAllForSelect();
  }

  @Get('/:complaintTypeId')
  async getComplaintTypeById(@Param('complaintTypeId', new ParseUUIDPipe()) complaintTypeId: string) {
    return this.complaintTypeService.getComplaintTypeById(complaintTypeId);
  }

  @Patch('/:complaintTypeId')
  async updateComplaintType(
    @Param('complaintTypeId', new ParseUUIDPipe()) complaintTypeId: string,
    @Body() updateComplaintTypeDto: UpdateComplaintTypeDto,
  ) {
    return this.complaintTypeService.updateComplaintType(complaintTypeId, updateComplaintTypeDto);
  }

  @Delete('/:complaintTypeId')
  async deleteComplaintType(@Param('complaintTypeId', new ParseUUIDPipe()) complaintTypeId: string) {
    return this.complaintTypeService.deleteComplaintType(complaintTypeId);
  }
}
