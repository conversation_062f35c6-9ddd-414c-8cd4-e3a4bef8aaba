import { ApiProperty } from '@nestjs/swagger';

export class StepResponseDto {
  @ApiProperty({
    description: 'ID único del paso',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Título del paso',
    example: 'Llenar el formulario de solicitud',
  })
  title: string;

  @ApiProperty({
    description: 'Descripción detallada del paso',
    example: 'El usuario debe completar el formulario con la información requerida.',
  })
  description: string;

  @ApiProperty({
    description: 'Orden del paso dentro del protocolo',
    example: 1,
  })
  order: number;

  @ApiProperty({
    description: 'ID del protocolo al que pertenece este paso',
    example: 'protocol-uuid',
  })
  protocolId: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '<PERSON>cha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
