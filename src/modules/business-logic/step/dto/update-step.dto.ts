import { PartialType } from '@nestjs/mapped-types';
import { CreateStepDto } from './create-step.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, IsString } from 'class-validator';

export class UpdateStepDto extends PartialType(CreateStepDto) {
  @ApiProperty({
    description: 'Orden actualizado del paso dentro del protocolo',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  order?: number;

  @ApiProperty({
    description: 'Descripción actualizada del paso',
    example: 'Verificar la documentación antes de enviar',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
