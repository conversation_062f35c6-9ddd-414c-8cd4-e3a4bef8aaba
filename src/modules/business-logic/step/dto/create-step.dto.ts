import { IsString, IsUUID, IsInt, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateStepDto {
  @ApiProperty({
    description: 'Título del paso',
    example: 'Llenar el formulario de solicitud',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Orden del paso dentro del protocolo',
    example: 1,
  })
  @IsInt()
  @Min(1)
  order: number;

  @ApiProperty({
    description: 'ID del protocolo al que pertenece este paso',
    example: 'protocol-uuid',
  })
  @IsUUID()
  protocolId: string;
}
