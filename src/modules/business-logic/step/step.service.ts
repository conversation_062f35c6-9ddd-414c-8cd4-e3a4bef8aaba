import { Injectable } from '@nestjs/common';
import { CreateStepDto } from './dto/create-step.dto';
import { UpdateStepDto } from './dto/update-step.dto';
import { StepPersistenceService } from 'src/modules/persistence/step-persistence.service';

@Injectable()
export class StepService {
  constructor(private readonly stepPersistence: StepPersistenceService) {}

  async create(data: CreateStepDto) {
    return this.stepPersistence.create(data);
  }

  async findAll() {
    return this.stepPersistence.findAll();
  }

  async findById(stepId: string) {
    return this.stepPersistence.findById(stepId);
  }

  async update(stepId: string, data: UpdateStepDto) {
    return this.stepPersistence.update(stepId, data);
  }

  async delete(stepId: string) {
    return this.stepPersistence.delete(stepId);
  }
}
