import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { StepService } from './step.service';
import { CreateStepDto } from './dto/create-step.dto';
import { UpdateStepDto } from './dto/update-step.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Step')
@Controller('step')
export class StepController {
  constructor(private readonly stepService: StepService) {}

  @ApiOperation({ summary: 'Registrar un nuevo paso en un protocolo' })
  @ApiResponse({ status: 201, description: 'Paso creado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateStepDto) {
    return this.stepService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los pasos' })
  @ApiResponse({ status: 200, description: 'Lista de pasos registrados' })
  @Get()
  async findAll() {
    return this.stepService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un paso por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del paso' })
  @Get('/:stepId')
  async findById(@Param('stepId') stepId: string) {
    return this.stepService.findById(stepId);
  }

  @ApiOperation({ summary: 'Actualizar un paso' })
  @ApiResponse({ status: 200, description: 'Paso actualizado exitosamente' })
  @Patch('/:stepId')
  async update(@Param('stepId') stepId: string, @Body() updateDto: UpdateStepDto) {
    return this.stepService.update(stepId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un paso' })
  @ApiResponse({ status: 200, description: 'Paso eliminado exitosamente' })
  @Delete('/:stepId')
  async delete(@Param('stepId') stepId: string) {
    return this.stepService.delete(stepId);
  }
}
