import { IsUUI<PERSON>, Is<PERSON><PERSON>, <PERSON>N<PERSON>ber, IsOptional, IsString, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ReservationStatus } from '@prisma/client';

export class CreateReservationDto {
  @ApiProperty({
    description: 'ID de la propiedad que realiza la reservación',
    example: 'property-uuid',
  })
  @IsUUID()
  propertyId: string;

  @ApiProperty({
    description: 'ID de la instalación reservada',
    example: 'facility-uuid',
  })
  @IsUUID()
  facilityId: string;

  @IsNumber()
  amountOfPeople: number;

  @IsDateString()
  startDateTime: Date;

  @IsDateString()
  endDateTime: Date;

  @ApiProperty({
    description: 'Estado de la reserva',
    example: 'PENDING',
    enum: ReservationStatus,
  })
  @IsOptional()
  @IsEnum(ReservationStatus)
  status?: ReservationStatus;

  @IsUUID()
  requestedBy: string;

  @IsOptional()
  @IsUUID()
  authorizedBy?: string;

  @IsOptional()
  @IsUUID()
  deniedBy?: string;

  @IsOptional()
  @IsString()
  deniedReason?: string;
}
