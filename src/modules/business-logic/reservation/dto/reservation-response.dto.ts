import { ApiProperty } from '@nestjs/swagger';
import { ReservationStatus } from '@prisma/client';

export class ReservationResponseDto {
  @ApiProperty({
    description: 'ID único de la reserva',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID de la propiedad que realiza la reserva',
    example: 'property-uuid',
  })
  propertyId: string;

  @ApiProperty({
    description: 'ID de la instalación reservada',
    example: 'facility-uuid',
  })
  facilityId: string;

  @ApiProperty({
    description: 'Fecha y hora de la reserva',
    example: '2024-04-15T14:00:00.000Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Estado de la reserva',
    example: 'PENDING',
    enum: ReservationStatus,
  })
  status: ReservationStatus;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
