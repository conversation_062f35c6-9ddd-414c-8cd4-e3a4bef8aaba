import { Injectable } from '@nestjs/common';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { ReservationPersistenceService } from '../../persistence/reservation-persistence.service';
import { DenyReservationDto } from './dto/deny-reservation.dto';

@Injectable()
export class ReservationService {
  constructor(private readonly reservationPersistence: ReservationPersistenceService) {}

  async create(data: CreateReservationDto) {
    return this.reservationPersistence.create(data);
  }

  async findAll() {
    return this.reservationPersistence.findAll();
  }

  async findById(reservationId: string) {
    return this.reservationPersistence.findById(reservationId);
  }

  async update(reservationId: string, data: UpdateReservationDto) {
    return this.reservationPersistence.update(reservationId, data);
  }

  async delete(reservationId: string) {
    return this.reservationPersistence.delete(reservationId);
  }

  async authorizeReservation(id: string, userId: string) {
    return this.reservationPersistence.authorize(id, userId);
  }

  async denyReservation(id: string, dto: DenyReservationDto, userId: string) {
    return this.reservationPersistence.deny(id, dto, userId);
  }

  async getReservationsByUserId(userId: string) {
    return this.reservationPersistence.getReservationsByUserId(userId);
  }

  async findFacilityReservations(facilityId: string) {
    return this.reservationPersistence.findFacilityReservations(facilityId);
  }
}
