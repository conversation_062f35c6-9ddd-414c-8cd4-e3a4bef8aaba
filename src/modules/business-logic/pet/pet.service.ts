import { Injectable } from '@nestjs/common';
import { CreatePetDto } from './dto/create-pet.dto';
import { UpdatePetDto } from './dto/update-pet.dto';
import { PetPersistenceService } from '../../persistence/pet-persistence.service';

@Injectable()
export class PetService {
  constructor(private readonly petPersistence: PetPersistenceService) {}

  async createPet(data: CreatePetDto) {
    return this.petPersistence.createPet(data);
  }

  async findAllPets() {
    return this.petPersistence.findAllPets();
  }

  async getPetById(petId: string) {
    return this.petPersistence.getPetById(petId);
  }

  async updatePet(petId: string, data: UpdatePetDto) {
    return this.petPersistence.updatePet(petId, data);
  }

  async deletePet(petId: string) {
    return this.petPersistence.deletePet(petId);
  }
}
