import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { PetService } from './pet.service';
import { CreatePetDto } from './dto/create-pet.dto';
import { UpdatePetDto } from './dto/update-pet.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Pet')
@Controller('pet')
export class PetController {
  constructor(private readonly petService: PetService) {}

  @ApiOperation({ summary: 'Registrar una mascota' })
  @ApiResponse({ status: 201, description: 'Mascota registrada exitosamente' })
  @Post()
  async createPet(@Body() createDto: CreatePetDto) {
    return this.petService.createPet(createDto);
  }

  @ApiOperation({ summary: 'Obtener todas las mascotas' })
  @ApiResponse({ status: 200, description: 'Lista de mascotas' })
  @Get()
  async findAllPets() {
    return this.petService.findAllPets();
  }

  @ApiOperation({ summary: 'Obtener una mascota por ID' })
  @ApiResponse({ status: 200, description: 'Detalles de la mascota' })
  @Get('/:petId')
  async getPetById(@Param('petId') petId: string) {
    return this.petService.getPetById(petId);
  }

  @ApiOperation({ summary: 'Actualizar una mascota' })
  @ApiResponse({ status: 200, description: 'Mascota actualizada exitosamente' })
  @Patch('/:petId')
  async updatePet(@Param('petId') petId: string, @Body() updateDto: UpdatePetDto) {
    return this.petService.updatePet(petId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar una mascota' })
  @ApiResponse({ status: 200, description: 'Mascota eliminada exitosamente' })
  @Delete('/:petId')
  async deletePet(@Param('petId') petId: string) {
    return this.petService.deletePet(petId);
  }
}
