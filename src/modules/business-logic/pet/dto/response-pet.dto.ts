import { ApiProperty } from '@nestjs/swagger';

export class PetResponseDto {
  @ApiProperty({
    description: 'ID único de la mascota',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Nombre de la mascota',
    example: 'Rocky',
  })
  name: string;

  @ApiProperty({
    description: 'Tipo de mascota',
    example: 'Perro',
  })
  type: string;

  @ApiProperty({
    description: 'ID de la propiedad a la que pertenece',
    example: 'property-uuid',
  })
  propertyId: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-02-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-02-10T15:30:00.000Z',
  })
  updatedAt: Date;
}
