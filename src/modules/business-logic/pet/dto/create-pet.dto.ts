import { IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePetDto {
  @ApiProperty({
    description: 'Nombre de la mascota',
    example: '<PERSON>',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> de mascota (Ej: Perro, Gato, etc.)',
    example: 'Perro',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'ID de la propiedad a la que pertenece la mascota',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  propertyId: string;
}
