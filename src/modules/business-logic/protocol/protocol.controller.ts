import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { ProtocolService } from './protocol.service';
import { CreateProtocolDto } from './dto/create-protocol.dto';
import { UpdateProtocolDto } from './dto/update-protocol.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Protocol')
@Controller('protocol')
export class ProtocolController {
  constructor(private readonly protocolService: ProtocolService) {}

  @ApiOperation({ summary: 'Registrar un protocolo' })
  @ApiResponse({ status: 201, description: 'Protocolo registrado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateProtocolDto) {
    return this.protocolService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los protocolos' })
  @ApiResponse({ status: 200, description: 'Lista de protocolos' })
  @Get()
  async findAll() {
    return this.protocolService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un protocolo por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del protocolo' })
  @Get('/:protocolId')
  async findById(@Param('protocolId') protocolId: string) {
    return this.protocolService.findById(protocolId);
  }

  @ApiOperation({ summary: 'Actualizar un protocolo' })
  @ApiResponse({ status: 200, description: 'Protocolo actualizado exitosamente' })
  @Patch('/:protocolId')
  async update(@Param('protocolId') protocolId: string, @Body() updateDto: UpdateProtocolDto) {
    return this.protocolService.update(protocolId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un protocolo' })
  @ApiResponse({ status: 200, description: 'Protocolo eliminado exitosamente' })
  @Delete('/:protocolId')
  async delete(@Param('protocolId') protocolId: string) {
    return this.protocolService.delete(protocolId);
  }
}
