import { Injectable } from '@nestjs/common';
import { CreateProtocolDto } from './dto/create-protocol.dto';
import { UpdateProtocolDto } from './dto/update-protocol.dto';
import { ProtocolPersistenceService } from '../../persistence/protocol-persistence.service';

@Injectable()
export class ProtocolService {
  constructor(private readonly protocolPersistence: ProtocolPersistenceService) {}

  async create(data: CreateProtocolDto) {
    return this.protocolPersistence.create(data);
  }

  async findAll() {
    return this.protocolPersistence.findAll();
  }

  async findById(protocolId: string) {
    return this.protocolPersistence.findById(protocolId);
  }

  async update(protocolId: string, data: UpdateProtocolDto) {
    return this.protocolPersistence.update(protocolId, data);
  }

  async delete(protocolId: string) {
    return this.protocolPersistence.delete(protocolId);
  }
}
