import { PartialType } from '@nestjs/mapped-types';
import { CreateProtocolDto } from './create-protocol.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsUUID } from 'class-validator';

export class UpdateProtocolDto extends PartialType(CreateProtocolDto) {
  @ApiProperty({
    description: 'ID de la instalación (facility) asociada al protocolo',
    example: 'facility-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  facilityId?: string;
}
