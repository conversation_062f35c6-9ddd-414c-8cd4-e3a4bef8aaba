import { ApiProperty } from '@nestjs/swagger';

export class ProtocolResponseDto {
  @ApiProperty({
    description: 'ID único del protocolo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Título del protocolo',
    example: 'Protocolo de seguridad en piscina',
  })
  title: string;

  @ApiProperty({
    description: 'Descripción del protocolo',
    example: 'Reglas de seguridad para el uso de la piscina',
  })
  description: string;

  @ApiProperty({
    description: 'ID de la instalación asociada (opcional)',
    example: 'facility-uuid',
  })
  facilityId?: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-02-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-02-10T15:30:00.000Z',
  })
  updatedAt: Date;
}
