import { IsString, IsOptional, IsUUID, Is<PERSON>rray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateStepDto } from 'src/modules/business-logic/step/dto/create-step.dto';
import { StepForProtocolDto } from 'src/modules/business-logic/step/dto/step-for-protocol.dto';

export class CreateProtocolDto {
  @ApiProperty({
    description: 'Título del protocolo',
    example: 'Protocolo de seguridad en piscina',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Descripción del protocolo',
    example: 'Reglas de seguridad para el uso de la piscina',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'ID de la instalación asociada (opcional)',
    example: 'facility-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  facilityId?: string;

  @ApiProperty({
    description: 'Lista de pasos para el protocolo',
    type: [CreateStepDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StepForProtocolDto)
  steps?: StepForProtocolDto[];
}
