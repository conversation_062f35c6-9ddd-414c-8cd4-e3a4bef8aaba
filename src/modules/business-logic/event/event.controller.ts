import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { EventService } from './event.service';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';

@Controller('event')
export class EventController {
  constructor(private readonly eventService: EventService) {}

  @Post()
  async createEvent(@Body() createDto: CreateEventDto) {
    return this.eventService.createEvent(createDto);
  }

  @Get()
  async findAllEvents() {
    return this.eventService.findAllEvents();
  }

  @Get('/:eventId')
  async getEventById(@Param('eventId') eventId: string) {
    return this.eventService.getEventById(eventId);
  }

  @Patch('/:eventId')
  async updateEvent(@Param('eventId') eventId: string, @Body() updateDto: UpdateEventDto) {
    return this.eventService.updateEvent(eventId, updateDto);
  }

  @Delete('/:eventId')
  async deleteEvent(@Param('eventId') eventId: string) {
    return this.eventService.deleteEvent(eventId);
  }
}
