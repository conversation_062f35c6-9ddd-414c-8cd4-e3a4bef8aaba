import { Role } from '@prisma/client';
import { IsString, IsOptional, IsDateString, IsNotEmpty, IsArray } from 'class-validator';

export class CreateEventDto {
  @IsString()
  title: string;

  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  location: string;

  @IsDateString()
  startDate: Date;

  @IsDateString()
  endDate: Date;

  @IsNotEmpty()
  @IsArray()
  roles: Role['id'][];
}
