import { Injectable } from '@nestjs/common';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { EventPersistenceService } from '../../persistence/event-persistence.service';

@Injectable()
export class EventService {
  constructor(private readonly eventPersistence: EventPersistenceService) {}

  async createEvent(data: CreateEventDto) {
    return this.eventPersistence.createEvent(data);
  }

  async findAllEvents() {
    return this.eventPersistence.findAllEvents();
  }

  async getEventById(eventId: string) {
    return this.eventPersistence.getEventById(eventId);
  }

  async updateEvent(eventId: string, data: UpdateEventDto) {
    return this.eventPersistence.updateEvent(eventId, data);
  }

  async deleteEvent(eventId: string) {
    return this.eventPersistence.deleteEvent(eventId);
  }
}
