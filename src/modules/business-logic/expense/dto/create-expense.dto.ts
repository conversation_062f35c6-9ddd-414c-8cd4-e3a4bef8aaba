import { IsString, IsN<PERSON>ber, IsOptional, IsDateString } from 'class-validator';

export class CreateExpenseDto {
  @IsString()
  description: string;

  @IsNumber()
  amount: number;

  @IsDateString()
  @IsOptional()
  date?: Date;

  @IsString()
  @IsOptional()
  receipt?: string; // URL del recibo

  @IsString()
  @IsOptional()
  serviceId?: string;

  @IsString()
  @IsOptional()
  supplierId?: string;
}
