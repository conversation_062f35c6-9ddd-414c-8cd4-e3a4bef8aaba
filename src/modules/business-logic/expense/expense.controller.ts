import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { ExpenseService } from './expense.service';
import { CreateExpenseDto } from './dto/create-expense.dto';
import { UpdateExpenseDto } from './dto/update-expense.dto';

@Controller('expense')
export class ExpenseController {
  constructor(private readonly expenseService: ExpenseService) {}

  @Post()
  async createExpense(@Body() createDto: CreateExpenseDto) {
    return this.expenseService.createExpense(createDto);
  }

  @Get()
  async findAllExpenses() {
    return this.expenseService.findAllExpenses();
  }

  @Get('/:expenseId')
  async getExpenseById(@Param('expenseId') expenseId: string) {
    return this.expenseService.getExpenseById(expenseId);
  }

  @Patch('/:expenseId')
  async updateExpense(@Param('expenseId') expenseId: string, @Body() updateDto: UpdateExpenseDto) {
    return this.expenseService.updateExpense(expenseId, updateDto);
  }

  @Delete('/:expenseId')
  async deleteExpense(@Param('expenseId') expenseId: string) {
    return this.expenseService.deleteExpense(expenseId);
  }
}
