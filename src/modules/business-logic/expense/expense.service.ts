import { Injectable } from '@nestjs/common';
import { CreateExpenseDto } from './dto/create-expense.dto';
import { UpdateExpenseDto } from './dto/update-expense.dto';
import { ExpensePersistenceService } from 'src/modules/persistence/expense-persistence.service';

@Injectable()
export class ExpenseService {
  constructor(private readonly expensePersistence: ExpensePersistenceService) {}

  async createExpense(data: CreateExpenseDto) {
    return this.expensePersistence.createExpense(data);
  }

  async findAllExpenses() {
    return this.expensePersistence.findAllExpenses();
  }

  async getExpenseById(expenseId: string) {
    return this.expensePersistence.getExpenseById(expenseId);
  }

  async updateExpense(expenseId: string, data: UpdateExpenseDto) {
    return this.expensePersistence.updateExpense(expenseId, data);
  }

  async deleteExpense(expenseId: string) {
    return this.expensePersistence.deleteExpense(expenseId);
  }
}
