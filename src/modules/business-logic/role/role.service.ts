import { Injectable } from '@nestjs/common';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { RolesPersistenceService } from '../../persistence/roles-persistence.service';
import { Role } from '@prisma/client';
import { ErrorHandlerService } from 'src/modules/error-handler/error-handler.service';

@Injectable()
export class RoleService {
  constructor(
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly rolesPersistenceService: RolesPersistenceService,
  ) {}

  async create(createRoleDto: CreateRoleDto) {
    return await this.rolesPersistenceService.create(createRoleDto);
  }

  async findAll() {
    try {
      return await this.rolesPersistenceService.findAll();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all users", error.statusCode);
    }
  }

  async findAllForSelect() {
    try {
      return await this.rolesPersistenceService.findAllForSelect();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all users", error.statusCode);
    }
  }

  findOne(id: number) {
    return `This action returns a #${id} role`;
  }

  async update(id: Role['id'], updateRoleDto: UpdateRoleDto) {
    try {
      return await this.rolesPersistenceService.update(id, updateRoleDto);
    } catch (error) {}
  }

  remove(id: number) {
    return `This action removes a #${id} role`;
  }
}
