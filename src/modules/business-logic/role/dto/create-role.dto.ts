import { ApiProperty } from '@nestjs/swagger';
import { Role } from '@prisma/client';
import { IsNotEmpty, IsString } from 'class-validator';

export interface CreateUserRequest extends Omit<Role, 'id'> {}
export class CreateRoleDto {
  @ApiProperty({ description: 'Nombre del rol', example: 'Resident' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Descripción del rol', example: 'Rol para identificar residentes' })
  @IsNotEmpty()
  @IsString()
  description: string;
}
