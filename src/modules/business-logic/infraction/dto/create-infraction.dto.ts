import { IsString, IsEnum, IsOptional, IsDate } from 'class-validator';
import { InfractionSeverity } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class CreateInfractionDto {
  @ApiProperty({
    description: 'Descripción de la falta cometida',
    example: 'Uso indebido de áreas comunes',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Fecha en la que ocurrió la falta',
    example: '2024-02-01T10:00:00.000Z',
  })
  @Transform(({ value }) => {
    const dateString = JSON.parse(value);
    return new Date(dateString);
  })
  @IsDate()
  @IsOptional()
  date: Date;

  @ApiProperty({
    description: 'Nivel de gravedad de la falta',
    enum: InfractionSeverity,
    example: InfractionSeverity.MODERATE,
  })
  @IsEnum(InfractionSeverity)
  severity: InfractionSeverity;

  @ApiProperty({
    description: 'ID de la propiedad asociada a la falta',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  propertyId: string;
}
