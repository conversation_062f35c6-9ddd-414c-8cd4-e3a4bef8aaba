import { InfractionSeverity } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';

export class InfractionResponseDto {
  @ApiProperty({
    description: 'ID único de la falta',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Descripción de la falta',
    example: 'Ruido excesivo en la madrugada',
  })
  description: string;

  @ApiProperty({
    description: 'Fecha de la falta',
    example: '2024-02-01T10:00:00.000Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Severidad de la falta',
    example: InfractionSeverity.SEVERE,
  })
  severity: InfractionSeverity;

  @ApiProperty({
    description: 'ID de la propiedad asociada',
    example: 'property-uuid',
  })
  propertyId: string;
}
