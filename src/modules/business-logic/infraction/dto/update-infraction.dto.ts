import { PartialType } from '@nestjs/mapped-types';
import { CreateInfractionDto } from './create-infraction.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { MaintenanceIssueImage } from '@prisma/client';

export class UpdateInfractionDto extends PartialType(CreateInfractionDto) {
  @ApiProperty({
    description: 'Fecha de actualización de la falta',
    example: '2024-02-10T15:30:00.000Z',
    required: false,
  })
  date?: Date;
  @IsOptional()
  @IsString({ each: true })
  images?: MaintenanceIssueImage[];
}
