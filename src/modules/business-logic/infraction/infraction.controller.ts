import { Controller, Post, Get, Patch, Delete, Body, Param, UseInterceptors, UploadedFiles } from '@nestjs/common';
import { InfractionService } from './infraction.service';
import { CreateInfractionDto } from './dto/create-infraction.dto';
import { UpdateInfractionDto } from './dto/update-infraction.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';

@ApiTags('Infraction')
@Controller('infraction')
export class InfractionController {
  constructor(private readonly infractionService: InfractionService) {}

  @ApiOperation({ summary: 'Crear una infracción' })
  @ApiResponse({ status: 201, description: 'Infracción creada exitosamente' })
  @Post()
  @UseInterceptors(FilesInterceptor('files', 3))
  async createInfraction(@Body() createDto: CreateInfractionDto, @UploadedFiles() files: Express.Multer.File[]) {
    return this.infractionService.createInfraction(createDto, files);
  }

  @ApiOperation({ summary: 'Obtener todas las infracciones' })
  @ApiResponse({ status: 200, description: 'Lista de infracciones' })
  @Get()
  async findAllInfractions() {
    return this.infractionService.findAllInfractions();
  }

  @ApiOperation({ summary: 'Obtener una infracción por ID' })
  @ApiResponse({ status: 200, description: 'Detalles de la infracción' })
  @Get('/:infractionId')
  async getInfractionById(@Param('infractionId') infractionId: string) {
    return this.infractionService.getInfractionById(infractionId);
  }

  @ApiOperation({ summary: 'Actualizar una infracción' })
  @ApiResponse({ status: 200, description: 'Infracción actualizada exitosamente' })
  @Patch('/:infractionId')
  @UseInterceptors(FilesInterceptor('files', 3))
  async updateInfraction(
    @Param('infractionId') infractionId: string,
    @Body() updateDto: UpdateInfractionDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.infractionService.updateInfraction(infractionId, updateDto, files);
  }

  @ApiOperation({ summary: 'Eliminar una infracción' })
  @ApiResponse({ status: 200, description: 'Infracción eliminada exitosamente' })
  @Delete('/:infractionId')
  async deleteInfraction(@Param('infractionId') infractionId: string) {
    return this.infractionService.deleteInfraction(infractionId);
  }
}
