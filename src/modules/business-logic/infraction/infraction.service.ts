import { Injectable } from '@nestjs/common';
import { CreateInfractionDto } from './dto/create-infraction.dto';
import { UpdateInfractionDto } from './dto/update-infraction.dto';
import { InfractionPersistenceService } from 'src/modules/persistence/infraction-persistence.service';
import { SupabaseService } from 'src/modules/storage/supabase.service';
import { randomUUID } from 'crypto';

@Injectable()
export class InfractionService {
  constructor(
    private readonly infractionPersistence: InfractionPersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async createInfraction(data: CreateInfractionDto, files: Express.Multer.File[]) {
    const infraction = await this.infractionPersistence.createInfraction(data);
    files.forEach(async (file, index) => {
      const extension = file.originalname.split('.').pop();
      const path = `infractions/${infraction.id}-${index + 1}.${extension}`;

      const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

      await this.infractionPersistence.saveImagePath(infraction.id, uploadedPath);
    });

    return infraction;
  }

  async findAllInfractions() {
    const infractions = await this.infractionPersistence.findAllInfractions();
    for (const infraction of infractions) {
      if (infraction.images.length > 0) {
        infraction.images = await Promise.all(
          infraction.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }
    return infractions;
  }

  async getInfractionById(infractionId: string) {
    const infraction = await this.infractionPersistence.getInfractionById(infractionId);
    if (infraction.images.length > 0) {
      infraction.images = await Promise.all(
        infraction.images.map(async (image) => {
          const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
          return {
            ...image,
            path: signedUrl,
          };
        }),
      );
    }
    return infraction;
  }

  async updateInfraction(infractionId: string, data: UpdateInfractionDto, files: Express.Multer.File[] = []) {
    const updatedInfraction = await this.infractionPersistence.updateInfraction(infractionId, data);
    try {
      const parsedImages: { id: string; path: string }[] =
        typeof data.images === 'string' ? JSON.parse(data.images) : (data.images ?? []);

      const imagesToKeep = new Set(parsedImages.map((img) => img.id));
      const current = await this.infractionPersistence.getInfractionById(infractionId);

      const imagesToDelete = current.images.filter((img) => !imagesToKeep.has(img.id));

      for (const image of imagesToDelete) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
        await this.infractionPersistence.deleteImageById(image.id);
      }

      for (const file of files) {
        const extension = file.originalname.split('.').pop();
        const path = `infractions/${updatedInfraction.id}-${randomUUID()}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.infractionPersistence.saveImagePath(updatedInfraction.id, uploadedPath);
      }

      return updatedInfraction;
    } catch (error: any) {
      throw new Error(`Error updating maintenance issue report: ${error.message}`);
    }
  }

  async deleteInfraction(infractionId: string) {
    const infraction = await this.infractionPersistence.getInfractionById(infractionId);

    const images = infraction.images;
    for (const image of images) {
      await this.supabaseService.deleteFile('sabino-zibata', image.path);
    }

    return this.infractionPersistence.deleteInfraction(infractionId);
  }
}
