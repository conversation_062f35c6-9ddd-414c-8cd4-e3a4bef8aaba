import { Module } from '@nestjs/common';
import { InfractionService } from './infraction.service';
import { InfractionController } from './infraction.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';
import { StorageModule } from 'src/modules/storage/storage.module';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [InfractionController],
  providers: [InfractionService],
  exports: [InfractionService],
})
export class InfractionModule {}
