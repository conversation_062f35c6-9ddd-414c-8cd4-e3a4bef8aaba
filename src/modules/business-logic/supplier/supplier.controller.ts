import { Controller, Post, Get, Patch, Delete, Body, Param, ParseUUIDPipe } from '@nestjs/common';
import { SupplierService } from './supplier.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Supplier')
@Controller('supplier')
export class SupplierController {
  constructor(private readonly supplierService: SupplierService) {}

  @ApiOperation({ summary: 'Registrar un nuevo proveedor' })
  @ApiResponse({ status: 201, description: 'Proveedor creado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateSupplierDto) {
    return this.supplierService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los proveedores' })
  @ApiResponse({ status: 200, description: 'Lista de proveedores disponibles' })
  @Get()
  async findAll() {
    return this.supplierService.findAll();
  }

  @Get('for-select')
  findAllForSelect() {
    return this.supplierService.findAllForSelect();
  }

  @ApiOperation({ summary: 'Obtener un proveedor por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del proveedor' })
  @Get('/:supplierId')
  async findById(@Param('supplierId', new ParseUUIDPipe()) supplierId: string) {
    return this.supplierService.findById(supplierId);
  }

  @ApiOperation({ summary: 'Actualizar un proveedor' })
  @ApiResponse({ status: 200, description: 'Proveedor actualizado exitosamente' })
  @Patch('/:supplierId')
  async update(@Param('supplierId', new ParseUUIDPipe()) supplierId: string, @Body() updateDto: UpdateSupplierDto) {
    return this.supplierService.update(supplierId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un proveedor' })
  @ApiResponse({ status: 200, description: 'Proveedor eliminado exitosamente' })
  @Delete('/:supplierId')
  async delete(@Param('supplierId', new ParseUUIDPipe()) supplierId: string) {
    return this.supplierService.delete(supplierId);
  }
}
