import { Injectable } from '@nestjs/common';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { SupplierPersistenceService } from 'src/modules/persistence/supplier-persistence.service';

@Injectable()
export class SupplierService {
  constructor(private readonly supplierPersistence: SupplierPersistenceService) {}

  async create(data: CreateSupplierDto) {
    return this.supplierPersistence.create(data);
  }

  async findAll() {
    return this.supplierPersistence.findAll();
  }

  async findAllForSelect() {
    return await this.supplierPersistence.findAllForSelect();
  }

  async findById(supplierId: string) {
    return this.supplierPersistence.findById(supplierId);
  }

  async update(supplierId: string, data: UpdateSupplierDto) {
    return this.supplierPersistence.update(supplierId, data);
  }

  async delete(supplierId: string) {
    return this.supplierPersistence.delete(supplierId);
  }
}
