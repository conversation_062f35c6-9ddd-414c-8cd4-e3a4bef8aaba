import { ApiProperty } from '@nestjs/swagger';

export class SupplierResponseDto {
  @ApiProperty({
    description: 'ID único del proveedor',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nombre del proveedor',
    example: 'Servicios de Limpieza S.A.',
  })
  name: string;

  @ApiProperty({
    description: 'Teléfono del proveedor',
    example: '+52 ************',
  })
  phone?: string;

  @ApiProperty({
    description: 'Correo electrónico del proveedor',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiProperty({
    description: 'Dirección del proveedor',
    example: 'Av. Reforma #123, CDMX',
  })
  address?: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: '<PERSON>cha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
