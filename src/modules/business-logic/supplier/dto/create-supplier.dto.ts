import { IsString, IsOptional, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateSupplierDto {
  @ApiProperty({
    description: 'Nombre del proveedor',
    example: 'Servicios de Limpieza S.A.',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Teléfono del proveedor',
    example: '+52 ************',
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Correo electrónico del proveedor',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Dirección del proveedor',
    example: 'Av. Reforma #123, CDMX',
  })
  @IsOptional()
  @IsString()
  address?: string;
}
