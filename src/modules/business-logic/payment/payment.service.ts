import { Injectable } from '@nestjs/common';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { PaymentPersistenceService } from '../../persistence/payment-persistence.service';

@Injectable()
export class PaymentService {
  constructor(private readonly paymentPersistence: PaymentPersistenceService) {}

  async createPayment(data: CreatePaymentDto) {
    return this.paymentPersistence.createPayment(data);
  }

  async findAllPayments() {
    return this.paymentPersistence.findAllPayments();
  }

  async getPaymentById(paymentId: string) {
    return this.paymentPersistence.getPaymentById(paymentId);
  }

  async updatePayment(paymentId: string, data: UpdatePaymentDto) {
    return this.paymentPersistence.updatePayment(paymentId, data);
  }

  async deletePayment(paymentId: string) {
    return this.paymentPersistence.deletePayment(paymentId);
  }
}
