import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Payment')
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @ApiOperation({ summary: 'Registrar un pago' })
  @ApiResponse({ status: 201, description: 'Pago registrado exitosamente' })
  @Post()
  async createPayment(@Body() createDto: CreatePaymentDto) {
    return this.paymentService.createPayment(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los pagos' })
  @ApiResponse({ status: 200, description: 'Lista de pagos' })
  @Get()
  async findAllPayments() {
    return this.paymentService.findAllPayments();
  }

  @ApiOperation({ summary: 'Obtener un pago por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del pago' })
  @Get('/:paymentId')
  async getPaymentById(@Param('paymentId') paymentId: string) {
    return this.paymentService.getPaymentById(paymentId);
  }

  @ApiOperation({ summary: 'Actualizar un pago' })
  @ApiResponse({ status: 200, description: 'Pago actualizado exitosamente' })
  @Patch('/:paymentId')
  async updatePayment(@Param('paymentId') paymentId: string, @Body() updateDto: UpdatePaymentDto) {
    return this.paymentService.updatePayment(paymentId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un pago' })
  @ApiResponse({ status: 200, description: 'Pago eliminado exitosamente' })
  @Delete('/:paymentId')
  async deletePayment(@Param('paymentId') paymentId: string) {
    return this.paymentService.deletePayment(paymentId);
  }
}
