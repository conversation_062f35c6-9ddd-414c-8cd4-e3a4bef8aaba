import { ApiProperty } from '@nestjs/swagger';

export class PaymentResponseDto {
  @ApiProperty({
    description: 'ID único del pago',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Monto del pago',
    example: 1500.5,
  })
  amount: number;

  @ApiProperty({
    description: 'Fecha en que se realizó el pago',
    example: '2024-02-01T10:00:00.000Z',
  })
  paymentDate: Date;

  @ApiProperty({
    description: 'Descripción del pago',
    example: 'Pago de cuota de mantenimiento',
  })
  description: string;

  @ApiProperty({
    description: 'ID de la propiedad asociada',
    example: 'property-uuid',
  })
  propertyId: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-02-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-02-10T15:30:00.000Z',
  })
  updatedAt: Date;
}
