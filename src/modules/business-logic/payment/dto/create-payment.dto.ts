import { IsString, IsN<PERSON>ber, IsOptional, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePaymentDto {
  @ApiProperty({
    description: 'Monto del pago',
    example: 1500.5,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Fecha en que se realizó el pago',
    example: '2024-02-01T10:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  paymentDate?: Date;

  @ApiProperty({
    description: 'Descripción del pago (Ej: Cuota mensual, mantenimiento, etc.)',
    example: 'Pago de cuota de mantenimiento',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'ID de la propiedad asociada al pago',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  propertyId: string;
}
