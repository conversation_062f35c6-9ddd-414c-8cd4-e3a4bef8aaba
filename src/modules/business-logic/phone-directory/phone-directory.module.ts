import { Module } from '@nestjs/common';
import { PhoneDirectoryService } from './phone-directory.service';
import { PhoneDirectoryController } from './phone-directory.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';

@Module({
  imports: [PersistenceModule],
  controllers: [PhoneDirectoryController],
  providers: [PhoneDirectoryService],
  exports: [PhoneDirectoryService],
})
export class PhoneDirectoryModule {}
