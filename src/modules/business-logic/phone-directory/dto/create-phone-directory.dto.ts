import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePhoneDirectoryDto {
  @ApiProperty({
    description: 'Nombre del contacto en el directorio telefónico',
    example: 'Administración',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Número de teléfono del contacto',
    example: '+52 ************',
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;
}
