import { PartialType } from '@nestjs/mapped-types';
import { CreatePhoneDirectoryDto } from './create-phone-directory.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdatePhoneDirectoryDto extends PartialType(CreatePhoneDirectoryDto) {
  @ApiProperty({
    description: 'Nuevo nombre del contacto',
    example: 'Administración General',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Nuevo número de teléfono',
    example: '+52 ************',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}
