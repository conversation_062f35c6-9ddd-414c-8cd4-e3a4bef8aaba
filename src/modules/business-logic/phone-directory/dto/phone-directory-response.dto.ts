import { ApiProperty } from '@nestjs/swagger';

export class PhoneDirectoryResponseDto {
  @ApiProperty({
    description: 'ID único del contacto en el directorio',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nombre del contacto',
    example: 'Administración',
  })
  name: string;

  @ApiProperty({
    description: 'Número de teléfono del contacto',
    example: '+52 ************',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
