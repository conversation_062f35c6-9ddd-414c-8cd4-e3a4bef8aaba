import { Injectable } from '@nestjs/common';
import { CreatePhoneDirectoryDto } from './dto/create-phone-directory.dto';
import { UpdatePhoneDirectoryDto } from './dto/update-phone-directory.dto';
import { PhoneDirectoryPersistenceService } from '../../persistence/phone-directory-persistence.service';

@Injectable()
export class PhoneDirectoryService {
  constructor(private readonly phoneDirectoryPersistence: PhoneDirectoryPersistenceService) {}

  async create(data: CreatePhoneDirectoryDto) {
    return this.phoneDirectoryPersistence.create(data);
  }

  async findAll() {
    return this.phoneDirectoryPersistence.findAll();
  }

  async findById(phoneDirectoryId: string) {
    return this.phoneDirectoryPersistence.findById(phoneDirectoryId);
  }

  async update(phoneDirectoryId: string, data: UpdatePhoneDirectoryDto) {
    return this.phoneDirectoryPersistence.update(phoneDirectoryId, data);
  }

  async delete(phoneDirectoryId: string) {
    return this.phoneDirectoryPersistence.delete(phoneDirectoryId);
  }
}
