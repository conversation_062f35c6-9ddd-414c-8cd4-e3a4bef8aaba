import { Controller, Post, Get, Patch, Delete, Body, Param, ParseUUIDPipe } from '@nestjs/common';
import { PhoneDirectoryService } from './phone-directory.service';
import { CreatePhoneDirectoryDto } from './dto/create-phone-directory.dto';
import { UpdatePhoneDirectoryDto } from './dto/update-phone-directory.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PhoneDirectoryResponseDto } from './dto/phone-directory-response.dto';

@ApiTags('Phone Directory')
@Controller('phone-directory')
export class PhoneDirectoryController {
  constructor(private readonly phoneDirectoryService: PhoneDirectoryService) {}

  @ApiOperation({ summary: 'Crear un nuevo contacto en el directorio telefónico' })
  @ApiResponse({ 
    status: 201, 
    description: 'Contacto creado exitosamente',
    type: PhoneDirectoryResponseDto 
  })
  @Post()
  async create(@Body() createDto: CreatePhoneDirectoryDto) {
    return this.phoneDirectoryService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los contactos del directorio telefónico' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lista de contactos del directorio',
    type: [PhoneDirectoryResponseDto] 
  })
  @Get()
  async findAll() {
    return this.phoneDirectoryService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un contacto por ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Detalles del contacto',
    type: PhoneDirectoryResponseDto 
  })
  @Get('/:phoneDirectoryId')
  async findById(@Param('phoneDirectoryId', ParseUUIDPipe) phoneDirectoryId: string) {
    return this.phoneDirectoryService.findById(phoneDirectoryId);
  }

  @ApiOperation({ summary: 'Actualizar un contacto del directorio' })
  @ApiResponse({ 
    status: 200, 
    description: 'Contacto actualizado exitosamente',
    type: PhoneDirectoryResponseDto 
  })
  @Patch('/:phoneDirectoryId')
  async update(
    @Param('phoneDirectoryId', ParseUUIDPipe) phoneDirectoryId: string,
    @Body() updateDto: UpdatePhoneDirectoryDto
  ) {
    return this.phoneDirectoryService.update(phoneDirectoryId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un contacto del directorio' })
  @ApiResponse({ status: 200, description: 'Contacto eliminado exitosamente' })
  @Delete('/:phoneDirectoryId')
  async delete(@Param('phoneDirectoryId', ParseUUIDPipe) phoneDirectoryId: string) {
    return this.phoneDirectoryService.delete(phoneDirectoryId);
  }
}
