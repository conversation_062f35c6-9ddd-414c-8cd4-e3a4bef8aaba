import { Injectable } from '@nestjs/common';
import { ComplaintPersistenceService } from '../../persistence/complaint-persistence.service';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { UpdateComplaintDto } from './dto/update-complaint.dto';
import { SupabaseService } from 'src/modules/storage/supabase.service';
import { randomUUID } from 'crypto';

@Injectable()
export class ComplaintService {
  constructor(
    private readonly complaintPersistence: ComplaintPersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async createComplaint(data: CreateComplaintDto, files: Express.Multer.File[]) {
    const complaint = await this.complaintPersistence.createComplaint(data);

    if (files?.length > 0) {
      files.forEach(async (file, index) => {
        const extension = file.originalname.split('.').pop();
        const path = `complaints/${complaint.id}-${index + 1}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.complaintPersistence.saveImagePath(complaint.id, uploadedPath);
      });
    }
    return complaint;
  }

  async findAllComplaints() {
    const complaints = await this.complaintPersistence.findAllComplaints();
    for (const complaint of complaints) {
      if (complaint.images.length > 0) {
        complaint.images = await Promise.all(
          complaint.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }
    return complaints;
  }

  async getComplaintById(complaintId: string) {
    const complaint = await this.complaintPersistence.getComplaintById(complaintId);
    if (complaint.images.length > 0) {
      complaint.images = await Promise.all(
        complaint.images.map(async (image) => {
          const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
          return {
            ...image,
            path: signedUrl,
          };
        }),
      );
    }
    return complaint;
  }

  async updateComplaint(complaintId: string, data: UpdateComplaintDto, files: Express.Multer.File[] = []) {
    const updatedComplaint = await this.complaintPersistence.updateComplaint(complaintId, data);
    try {
      const parsedImages: { id: string; path: string }[] =
        typeof data.images === 'string' ? JSON.parse(data.images) : (data.images ?? []);

      const imagesToKeep = new Set(parsedImages.map((img) => img.id));
      const current = await this.complaintPersistence.getComplaintById(complaintId);

      const imagesToDelete = current.images.filter((img) => !imagesToKeep.has(img.id));

      for (const image of imagesToDelete) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
        await this.complaintPersistence.deleteImageById(image.id);
      }

      for (const file of files) {
        const extension = file.originalname.split('.').pop();
        const path = `infractions/${updatedComplaint.id}-${randomUUID()}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.complaintPersistence.saveImagePath(updatedComplaint.id, uploadedPath);
      }

      return updatedComplaint;
    } catch (error: any) {
      throw new Error(`Error updating maintenance issue report: ${error.message}`);
    }
  }

  async deleteComplaint(complaintId: string) {
    const complaint = await this.complaintPersistence.getComplaintById(complaintId);
    const images = complaint.images;
    for (const image of images) {
      await this.supabaseService.deleteFile('sabino-zibata', image.path);
    }

    return this.complaintPersistence.deleteComplaint(complaintId);
  }

  async getComplaintsByUserId(userId: string) {
    const complaints = await this.complaintPersistence.getComplaintsByUserId(userId);
    for (const complaint of complaints) {
      if (complaint.images.length > 0) {
        complaint.images = await Promise.all(
          complaint.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }
    return complaints;
  }

  async findAllComplaintTypes() {
    return this.complaintPersistence.findAllComplaintTypes();
  }
}
