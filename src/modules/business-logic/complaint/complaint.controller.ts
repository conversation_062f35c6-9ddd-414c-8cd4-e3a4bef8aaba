import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  ParseUUIDPipe,
  Patch,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { ComplaintService } from './complaint.service';
import { CreateComplaintDto } from './dto/create-complaint.dto';
import { UpdateComplaintDto } from './dto/update-complaint.dto';
import { FilesInterceptor } from '@nestjs/platform-express';

@Controller('complaint')
export class ComplaintController {
  constructor(private readonly complaintService: ComplaintService) {}

  @Post()
  @UseInterceptors(FilesInterceptor('files', 3))
  async createComplaint(@Body() createComplaintDto: CreateComplaintDto, @UploadedFiles() files: Express.Multer.File[]) {
    return this.complaintService.createComplaint(createComplaintDto, files);
  }

  @Get()
  async findAllComplaints() {
    return this.complaintService.findAllComplaints();
  }

  @Get()
  async findAllComplaintTypes() {
    return this.complaintService.findAllComplaintTypes();
  }

  @Get('/:complaintId')
  async getComplaintById(@Param('complaintId', new ParseUUIDPipe()) complaintId: string) {
    return this.complaintService.getComplaintById(complaintId);
  }

  @Patch('/:complaintId')
  async updateComplaint(
    @Param('complaintId', new ParseUUIDPipe()) complaintId: string,
    @Body() updateData: UpdateComplaintDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.complaintService.updateComplaint(complaintId, updateData, files);
  }

  @Delete('/:complaintId')
  async deleteComplaint(@Param('complaintId', new ParseUUIDPipe()) complaintId: string) {
    return this.complaintService.deleteComplaint(complaintId);
  }
}
