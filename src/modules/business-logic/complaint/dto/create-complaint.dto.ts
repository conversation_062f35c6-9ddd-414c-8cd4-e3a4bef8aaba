import { Status, Priority } from '@prisma/client';
import { IsString, IsNotEmpty, IsDateString, IsOptional } from 'class-validator';

export class CreateComplaintDto {
  @IsString()
  @IsNotEmpty()
  propertyId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  complaintTypeId: string;

  @IsString()
  @IsNotEmpty()
  detail: string;

  @IsOptional()
  @IsDateString()
  completedAt?: Date;

  @IsString()
  @IsNotEmpty()
  status: Status;

  @IsString()
  @IsNotEmpty()
  priority: Priority;
}
