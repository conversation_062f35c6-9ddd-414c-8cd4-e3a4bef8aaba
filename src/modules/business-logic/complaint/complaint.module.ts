import { Module } from '@nestjs/common';
import { ComplaintService } from './complaint.service';
import { ComplaintController } from './complaint.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';
import { StorageModule } from 'src/modules/storage/storage.module';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [ComplaintController],
  providers: [ComplaintService],
  exports: [ComplaintService],
})
export class ComplaintModule {}
