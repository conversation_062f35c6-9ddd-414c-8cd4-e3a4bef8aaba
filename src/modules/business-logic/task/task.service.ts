import { Injectable } from '@nestjs/common';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskPersistenceService } from 'src/modules/persistence/task-persistence.service';

@Injectable()
export class TaskService {
  constructor(private readonly taskPersistence: TaskPersistenceService) {}

  async create(data: CreateTaskDto) {
    return this.taskPersistence.create(data);
  }

  async findAll() {
    return this.taskPersistence.findAll();
  }

  async findById(taskId: string) {
    return this.taskPersistence.findById(taskId);
  }

  async update(taskId: string, data: UpdateTaskDto) {
    return this.taskPersistence.update(taskId, data);
  }

  async delete(taskId: string) {
    return this.taskPersistence.delete(taskId);
  }
}
