import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { TaskService } from './task.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Task')
@Controller('task')
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @ApiOperation({ summary: 'Registrar una nueva tarea' })
  @ApiResponse({ status: 201, description: 'Tarea creada exitosamente' })
  @Post()
  async create(@Body() createDto: CreateTaskDto) {
    return this.taskService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todas las tareas' })
  @ApiResponse({ status: 200, description: 'Lista de tareas disponibles' })
  @Get()
  async findAll() {
    return this.taskService.findAll();
  }

  @ApiOperation({ summary: 'Obtener una tarea por ID' })
  @ApiResponse({ status: 200, description: 'Detalles de la tarea' })
  @Get('/:taskId')
  async findById(@Param('taskId') taskId: string) {
    return this.taskService.findById(taskId);
  }

  @ApiOperation({ summary: 'Actualizar una tarea' })
  @ApiResponse({ status: 200, description: 'Tarea actualizada exitosamente' })
  @Patch('/:taskId')
  async update(@Param('taskId') taskId: string, @Body() updateDto: UpdateTaskDto) {
    return this.taskService.update(taskId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar una tarea' })
  @ApiResponse({ status: 200, description: 'Tarea eliminada exitosamente' })
  @Delete('/:taskId')
  async delete(@Param('taskId') taskId: string) {
    return this.taskService.delete(taskId);
  }
}
