import { ApiProperty } from '@nestjs/swagger';
import { TaskStatus } from '@prisma/client';

export class TaskResponseDto {
  @ApiProperty({
    description: 'ID único de la tarea',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Título de la tarea',
    example: 'Revisión de seguridad',
  })
  title: string;

  @ApiProperty({
    description: 'Descripción de la tarea',
    example: 'Verificar que todas las cámaras de seguridad estén funcionando correctamente',
  })
  description?: string;

  @ApiProperty({
    description: 'Estado de la tarea',
    example: 'PENDING',
    enum: TaskStatus,
  })
  status: TaskStatus;

  @ApiProperty({
    description: 'Fecha límite de la tarea',
    example: '2024-05-10T12:00:00.000Z',
  })
  dueDate: Date;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'ID del usuario asignado a la tarea',
    example: 'user-uuid',
    required: false,
  })
  userId?: string;
}
