import { IsString, IsEnum, IsOptional, IsUUID, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { TaskStatus } from '@prisma/client';

export class CreateTaskDto {
  @ApiProperty({
    description: 'Título o breve descripción de la tarea',
    example: 'Reparación de iluminación en el estacionamiento',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Descripción detallada de la tarea',
    example: 'Se debe revisar la iluminación del estacionamiento y cambiar focos fundidos.',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Estado de la tarea',
    example: 'PENDING',
    enum: TaskStatus,
  })
  @IsEnum(TaskStatus)
  status: TaskStatus;

  @ApiProperty({
    description: 'Fecha límite para completar la tarea',
    example: '2024-05-10T12:00:00.000Z',
  })
  @IsDateString()
  dueDate: Date;

  @ApiProperty({
    description: 'ID del usuario asignado a la tarea',
    example: 'user-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  userId?: string;
}
