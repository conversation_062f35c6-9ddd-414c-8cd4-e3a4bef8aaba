import { ApiProperty } from '@nestjs/swagger';

export class TagResponseDto {
  @ApiProperty({
    description: 'ID único del tag',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Nombre del tag',
    example: 'Acceso VIP',
  })
  name: string;

  @ApiProperty({
    description: 'Descripción del tag',
    example: 'Permite el acceso a áreas exclusivas',
  })
  description?: string;

  @ApiProperty({
    description: 'ID de la propiedad asociada',
    example: 'property-uuid',
  })
  propertyId: string;

  @ApiProperty({
    description: 'ID del usuario asociado (si aplica)',
    example: 'user-uuid',
  })
  userId?: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
