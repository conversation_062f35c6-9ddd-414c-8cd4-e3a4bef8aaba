import { PartialType } from '@nestjs/mapped-types';
import { CreateTagDto } from './create-tag.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateTagDto extends PartialType(CreateTagDto) {
  @ApiProperty({
    description: 'Nuevo nombre del tag',
    example: 'Acceso restringido',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Nueva descripción del tag',
    example: 'Restringe el acceso a ciertas áreas',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
