import { IsString, IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateTagDto {
  @ApiProperty({
    description: 'Nombre único del tag',
    example: 'Acceso VIP',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Descripción del tag',
    example: 'Permite el acceso a áreas exclusivas',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID de la propiedad a la que pertenece el tag',
    example: 'property-uuid',
  })
  @IsUUID()
  propertyId: string;

  @ApiProperty({
    description: 'ID del usuario que posee el tag (opcional)',
    example: 'user-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  userId?: string;
}
