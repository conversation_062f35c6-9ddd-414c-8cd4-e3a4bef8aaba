import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { TagService } from './tag.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Tag')
@Controller('tag')
export class TagController {
  constructor(private readonly tagService: TagService) {}

  @ApiOperation({ summary: 'Registrar un nuevo tag' })
  @ApiResponse({ status: 201, description: 'Tag creado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateTagDto) {
    return this.tagService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los tags' })
  @ApiResponse({ status: 200, description: 'Lista de tags registrados' })
  @Get()
  async findAll() {
    return this.tagService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un tag por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del tag' })
  @Get('/:tagId')
  async findById(@Param('tagId') tagId: string) {
    return this.tagService.findById(tagId);
  }

  @ApiOperation({ summary: 'Actualizar un tag' })
  @ApiResponse({ status: 200, description: 'Tag actualizado exitosamente' })
  @Patch('/:tagId')
  async update(@Param('tagId') tagId: string, @Body() updateDto: UpdateTagDto) {
    return this.tagService.update(tagId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un tag' })
  @ApiResponse({ status: 200, description: 'Tag eliminado exitosamente' })
  @Delete('/:tagId')
  async delete(@Param('tagId') tagId: string) {
    return this.tagService.delete(tagId);
  }
}
