import { Injectable } from '@nestjs/common';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { TagPersistenceService } from '../../persistence/tag-persistence.service';

@Injectable()
export class TagService {
  constructor(private readonly tagPersistence: TagPersistenceService) {}

  async create(data: CreateTagDto) {
    return this.tagPersistence.create(data);
  }

  async findAll() {
    return this.tagPersistence.findAll();
  }

  async findById(tagId: string) {
    return this.tagPersistence.findById(tagId);
  }

  async update(tagId: string, data: UpdateTagDto) {
    return this.tagPersistence.update(tagId, data);
  }

  async delete(tagId: string) {
    return this.tagPersistence.delete(tagId);
  }
}
