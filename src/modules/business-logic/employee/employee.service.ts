import { Injectable } from '@nestjs/common';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { EmployeePersistenceService } from '../../persistence/employee-persistence.service';

@Injectable()
export class EmployeeService {
  constructor(private readonly employeePersistence: EmployeePersistenceService) {}

  async createEmployee(data: CreateEmployeeDto) {
    return this.employeePersistence.createEmployee(data);
  }

  async findAllEmployees() {
    return this.employeePersistence.findAllEmployees();
  }

  async getEmployeeById(employeeId: string) {
    return this.employeePersistence.getEmployeeById(employeeId);
  }

  async updateEmployee(employeeId: string, data: UpdateEmployeeDto) {
    return this.employeePersistence.updateEmployee(employeeId, data);
  }

  async deleteEmployee(employeeId: string) {
    return this.employeePersistence.deleteEmployee(employeeId);
  }
}
