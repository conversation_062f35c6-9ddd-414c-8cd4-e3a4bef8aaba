import { IsString, IsOptional } from 'class-validator';

export class CreateEmployeeDto {
  @IsString()
  firstName: string;

  @IsString()
  paternalLastName: string;

  @IsString()
  maternalLastName: string;

  @IsString()
  position: string; // <PERSON>je<PERSON><PERSON>: "<PERSON><PERSON>", "Mantenimiento"

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsString()
  supplierId?: string; // ID del proveedor que emplea al trabajador
}
