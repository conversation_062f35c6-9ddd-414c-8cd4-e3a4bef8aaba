import { Controller, Post, Get, Delete, Body, Param, Patch } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';

@Controller('employee')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Post()
  async createEmployee(@Body() createDto: CreateEmployeeDto) {
    return this.employeeService.createEmployee(createDto);
  }

  @Get()
  async findAllEmployees() {
    return this.employeeService.findAllEmployees();
  }

  @Get('/:employeeId')
  async getEmployeeById(@Param('employeeId') employeeId: string) {
    return this.employeeService.getEmployeeById(employeeId);
  }

  @Patch('/:employeeId')
  async updateEmployee(@Param('employeeId') employeeId: string, @Body() updateDto: UpdateEmployeeDto) {
    return this.employeeService.updateEmployee(employeeId, updateDto);
  }

  @Delete('/:employeeId')
  async deleteEmployee(@Param('employeeId') employeeId: string) {
    return this.employeeService.deleteEmployee(employeeId);
  }
}
