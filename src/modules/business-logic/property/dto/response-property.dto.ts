import { ApiProperty } from '@nestjs/swagger';
import { Property, PropertyType } from '@prisma/client';

export interface PropertyResponse extends Omit<Property, 'ownerId'> {}

export class PropertyResponseDto implements PropertyResponse {
  @ApiProperty({ description: 'Id de la propiedad', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  id: string;

  @ApiProperty({ description: 'Dirección', example: '1' })
  address: string;

  @ApiProperty({ description: 'Id de propietario', example: '' })
  ownerId: string;

  @ApiProperty({ description: 'Tipo de propiedad', example: 'HOUSE' })
  type: PropertyType;

  @ApiProperty({ description: 'Descripción de la amenidad', example: 'Gimnasio para todos los habitantes.' })
  description?: string;

  @ApiProperty({ description: 'Fecha de creación', example: '2024-11-15T05:00:14.855Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Fecha de actualización', example: '2024-11-15T05:00:14.855Z' })
  updatedAt: Date;
}
