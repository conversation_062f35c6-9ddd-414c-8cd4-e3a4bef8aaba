import { IsString, <PERSON>U<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateVehicleDto } from 'src/modules/business-logic/vehicle/dto/create-vehicle.dto';
import { CreateTagDto } from 'src/modules/business-logic/tag/dto/create-tag.dto';
import { PropertyType } from '@prisma/client';
import { CreatePetDto } from '../../pet/dto/create-pet.dto';

export class CreatePropertyDto {
  @ApiProperty({
    description: 'Dirección de la propiedad',
    example: 'Calle 123, Ciudad',
  })
  @IsString()
  address: string;

  @IsString()
  ownerId: string;

  @IsString()
  type: PropertyType;

  @ApiProperty({
    description: 'Lista de mascotas asociadas a la propiedad',
    type: [CreatePetDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePetDto)
  pets?: CreatePetDto[];

  @ApiProperty({
    description: 'Lista de vehículos asociados a la propiedad',
    type: [CreateVehicleDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVehicleDto)
  vehicles?: CreateVehicleDto[];

  @ApiProperty({
    description: 'Lista de tags asociados a la propiedad',
    type: [CreateTagDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateTagDto)
  tags?: CreateTagDto[];

  @ApiProperty({
    description: 'Lista de IDs de residentes asociados a la propiedad',
    example: ['user-uuid-1', 'user-uuid-2'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  residents?: string[];
}
