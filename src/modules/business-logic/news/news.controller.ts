import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { NewsService } from './news.service';
import { CreateNewsDto } from './dto/create-news.dto';
import { UpdateNewsDto } from './dto/update-news.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('News')
@Controller('news')
export class NewsController {
  constructor(private readonly newsService: NewsService) {}

  @ApiOperation({ summary: 'Crear una noticia' })
  @ApiResponse({ status: 201, description: 'Noticia creada exitosamente' })
  @Post()
  async createNews(@Body() createDto: CreateNewsDto) {
    return this.newsService.createNews(createDto);
  }

  @ApiOperation({ summary: 'Obtener todas las noticias' })
  @ApiResponse({ status: 200, description: 'Lista de noticias' })
  @Get()
  async findAllNews() {
    return this.newsService.findAllNews();
  }

  @ApiOperation({ summary: 'Obtener una noticia por ID' })
  @ApiResponse({ status: 200, description: 'Detalles de la noticia' })
  @Get('/:newsId')
  async getNewsById(@Param('newsId') newsId: string) {
    return this.newsService.getNewsById(newsId);
  }

  @ApiOperation({ summary: 'Actualizar una noticia' })
  @ApiResponse({ status: 200, description: 'Noticia actualizada exitosamente' })
  @Patch('/:newsId')
  async updateNews(@Param('newsId') newsId: string, @Body() updateDto: UpdateNewsDto) {
    return this.newsService.updateNews(newsId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar una noticia' })
  @ApiResponse({ status: 200, description: 'Noticia eliminada exitosamente' })
  @Delete('/:newsId')
  async deleteNews(@Param('newsId') newsId: string) {
    return this.newsService.deleteNews(newsId);
  }
}
