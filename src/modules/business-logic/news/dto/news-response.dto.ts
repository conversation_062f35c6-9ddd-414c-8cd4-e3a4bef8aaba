import { ApiProperty } from '@nestjs/swagger';

export class NewsResponseDto {
  @ApiProperty({
    description: 'ID único de la noticia',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Título de la noticia',
    example: 'Nueva normativa sobre el uso de áreas comunes',
  })
  title: string;

  @ApiProperty({
    description: 'Contenido de la noticia',
    example: 'A partir del 1 de marzo, se aplicarán nuevas normas...',
  })
  content: string;

  @ApiProperty({
    description: 'Fecha de publicación',
    example: '2024-02-01T10:00:00.000Z',
  })
  publishedAt: Date;

  @ApiProperty({
    description: 'Indica si la noticia está publicada',
    example: true,
  })
  isPublished: boolean;

  @ApiProperty({
    description: 'ID del usuario que creó la noticia',
    example: 'user-uuid',
  })
  userId?: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-02-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-02-10T15:30:00.000Z',
  })
  updatedAt: Date;
}
