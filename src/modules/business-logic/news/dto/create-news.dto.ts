import { IsString, IsOptional, IsBoolean, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateNewsDto {
  @ApiProperty({
    description: 'Título de la noticia',
    example: 'Nueva normativa sobre el uso de áreas comunes',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Contenido de la noticia',
    example: 'A partir del 1 de marzo, se aplicarán nuevas normas...',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Fecha de publicación',
    example: '2024-02-01T10:00:00.000Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  publishedAt?: Date;

  @ApiProperty({
    description: 'Indica si la noticia está publicada',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isPublished?: boolean;

  @ApiProperty({
    description: 'ID del usuario que creó la noticia',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsString()
  @IsOptional()
  userId?: string;
}
