import { Injectable } from '@nestjs/common';
import { CreateNewsDto } from './dto/create-news.dto';
import { UpdateNewsDto } from './dto/update-news.dto';
import { NewsPersistenceService } from '../../persistence/news-persistence.service';

@Injectable()
export class NewsService {
  constructor(private readonly newsPersistence: NewsPersistenceService) {}

  async createNews(data: CreateNewsDto) {
    return this.newsPersistence.createNews(data);
  }

  async findAllNews() {
    return this.newsPersistence.findAllNews();
  }

  async getNewsById(newsId: string) {
    return this.newsPersistence.getNewsById(newsId);
  }

  async updateNews(newsId: string, data: UpdateNewsDto) {
    return this.newsPersistence.updateNews(newsId, data);
  }

  async deleteNews(newsId: string) {
    return this.newsPersistence.deleteNews(newsId);
  }
}
