import { Controller, Post, Get, Delete, Body, Param, Patch, UploadedFiles, UseInterceptors } from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementDto } from './dto/create-announcement.dto';
import { UpdateAnnouncementDto } from './dto/update-announcement.dto';
import { MarkAnnouncementReadDto } from './dto/mark-announcement-read.dto';
import { FilesInterceptor } from '@nestjs/platform-express';

@Controller('announcement')
export class AnnouncementController {
  constructor(private readonly announcementService: AnnouncementService) {}

  // 🔹 Crear un nuevo anuncio
  @Post()
  @UseInterceptors(FilesInterceptor('files', 1))
  async createAnnouncement(@Body() createDto: CreateAnnouncementDto, @UploadedFiles() files: Express.Multer.File[]) {
    return this.announcementService.createAnnouncement(createDto, files);
  }

  // 🔹 Obtener todos los anuncios con destinatarios
  @Get()
  async findAllAnnouncements() {
    return this.announcementService.findAllAnnouncements();
  }

  // 🔹 Obtener un anuncio por ID
  @Get('/:announcementId')
  async getAnnouncementById(@Param('announcementId') announcementId: string) {
    return this.announcementService.getAnnouncementById(announcementId);
  }

  // 🔹 Actualizar un anuncio
  @Patch('/:announcementId')
  @UseInterceptors(FilesInterceptor('files', 1))
  async updateAnnouncement(
    @Param('announcementId') announcementId: string,
    @Body() updateDto: UpdateAnnouncementDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.announcementService.updateAnnouncement(announcementId, updateDto, files);
  }

  // 🔹 Eliminar un anuncio (los destinatarios también se eliminan por `onDelete: Cascade`)
  @Delete('/:announcementId')
  async deleteAnnouncement(@Param('announcementId') announcementId: string) {
    return this.announcementService.deleteAnnouncement(announcementId);
  }

  // 🔹 Marcar un anuncio como leído por un usuario
  @Patch('/mark-as-read')
  async markAsRead(@Body() markReadDto: MarkAnnouncementReadDto) {
    const { announcementId, userId } = markReadDto;
    return this.announcementService.markAsRead(announcementId, userId);
  }
}
