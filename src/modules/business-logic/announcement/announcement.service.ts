import { Injectable } from '@nestjs/common';
import { CreateAnnouncementDto } from './dto/create-announcement.dto';
import { UpdateAnnouncementDto } from './dto/update-announcement.dto';
import { AnnouncementPersistenceService } from '../../persistence/announcement-persistence.service';
import { SupabaseService } from 'src/modules/storage/supabase.service';
import { randomUUID } from 'crypto';

@Injectable()
export class AnnouncementService {
  constructor(
    private readonly announcementPersistence: AnnouncementPersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  // 🔹 Crear un anuncio y asignar destinatarios
  async createAnnouncement(data: CreateAnnouncementDto, files: Express.Multer.File[]) {
    const announcement = await this.announcementPersistence.createAnnouncement(data);
    files.forEach(async (file, index) => {
      const extension = file.originalname.split('.').pop();
      const path = `announcement/${announcement.id}-${index + 1}.${extension}`;

      const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

      await this.announcementPersistence.saveImagePath(announcement.id, uploadedPath);
    });

    return announcement;
  }

  // 🔹 Obtener todos los anuncios con destinatarios
  async findAllAnnouncements() {
    const announcements = await this.announcementPersistence.findAllAnnouncements();
    for (const announcement of announcements) {
      if (announcement.images.length > 0) {
        announcement.images = await Promise.all(
          announcement.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }

    return announcements;
  }

  // 🔹 Obtener un anuncio por ID con destinatarios
  async getAnnouncementById(announcementId: string) {
    const announcement = await this.announcementPersistence.getAnnouncementById(announcementId);
    if (announcement.images.length > 0) {
      announcement.images = await Promise.all(
        announcement.images.map(async (image) => {
          const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
          return {
            ...image,
            path: signedUrl,
          };
        }),
      );
    }
    return announcement;
  }

  // 🔹 Actualizar un anuncio (sin afectar destinatarios)
  async updateAnnouncement(announcementId: string, data: UpdateAnnouncementDto, files: Express.Multer.File[] = []) {
    const updatedAnnouncement = await this.announcementPersistence.updateAnnouncement(announcementId, data);
    try {
      const parsedImages: { id: string; path: string }[] =
        typeof data.images === 'string' ? JSON.parse(data.images) : (data.images ?? []);

      const imagesToKeep = new Set(parsedImages.map((img) => img.id));
      const current = await this.announcementPersistence.getAnnouncementById(announcementId);

      const imagesToDelete = current.images.filter((img) => !imagesToKeep.has(img.id));

      for (const image of imagesToDelete) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
        await this.announcementPersistence.deleteImageById(image.id);
      }

      for (const file of files) {
        const extension = file.originalname.split('.').pop();
        const path = `announcement/${updatedAnnouncement.id}-${randomUUID()}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.announcementPersistence.saveImagePath(updatedAnnouncement.id, uploadedPath);
      }

      return updatedAnnouncement;
    } catch (error: any) {
      throw new Error(`Error updating announcement: ${error.message}`);
    }
  }

  // 🔹 Eliminar un anuncio (Elimina también los destinatarios relacionados por `onDelete: Cascade`)
  async deleteAnnouncement(announcementId: string) {
    const announcement = await this.announcementPersistence.getAnnouncementById(announcementId);
    const images = announcement.images;
    if (images.length > 0) {
      for (const image of images) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
      }
    }
    return this.announcementPersistence.deleteAnnouncement(announcementId);
  }

  // 🔹 Marcar un anuncio como leído por un usuario
  async markAsRead(announcementId: string, userId: string) {
    return this.announcementPersistence.markAsRead(announcementId, userId);
  }
}
