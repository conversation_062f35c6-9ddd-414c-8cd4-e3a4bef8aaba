import { Injectable } from '@nestjs/common';
import { CreateFacilityDto } from './dto/create-facility.dto';
import { UpdateFacilityDto } from './dto/update-facility.dto';
import { ErrorHandlerService } from '../../error-handler/error-handler.service';
import { Facility } from '@prisma/client';
import { FacilityResponseDto } from './dto/response-facility.dto';
import { FacilityPersistenceService } from 'src/modules/persistence/facility-persistence.service';
import { FacilityMobileResponseDto } from './dto/facility-mobile-response.dto';
import { SupabaseService } from 'src/modules/storage/supabase.service';

@Injectable()
export class FacilityService {
  constructor(
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly facilityPersistenceService: FacilityPersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async create(createFacilityDto: CreateFacilityDto, file: Express.Multer.File) {
    try {
      const buffer = file.buffer; // Multer
      const path = await this.supabaseService.uploadFile(
        'sabino-zibata',
        `/facilities/${createFacilityDto.name}.jpg`,
        buffer,
        file.mimetype,
      );
      return await this.facilityPersistenceService.create({ ...createFacilityDto, imagePath: path });
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't create facility", error.statusCode);
    }
  }

  async findAll() {
    try {
      return await this.facilityPersistenceService.findAll();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all facilities", error.statusCode);
    }
  }

  async findAllForSelect() {
    try {
      return await this.facilityPersistenceService.findAllForSelect();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all facilities", error.statusCode);
    }
  }

  async findOne(id: Facility['id']): Promise<FacilityResponseDto> {
    try {
      const facility = await this.facilityPersistenceService.findById(id);
      const imagePath = await this.supabaseService.getSignedUrl('sabino-zibata', facility.imagePath);
      return { ...facility, imagePath };
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get facility", error.statusCode);
    }
  }

  async update(
    id: Facility['id'],
    updateFacilityDto: UpdateFacilityDto,
    file?: Express.Multer.File,
  ): Promise<FacilityResponseDto> {
    try {
      let path = updateFacilityDto.imagePath;

      if (file) {
        const buffer = file.buffer;
        path = await this.supabaseService.uploadFile(
          'sabino-zibata',
          `/facilities/${updateFacilityDto.name}.jpg`,
          buffer,
          file.mimetype,
        );
      }

      return await this.facilityPersistenceService.update(id, {
        ...updateFacilityDto,
        imagePath: path,
      });
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't update facility", error.statusCode);
    }
  }

  async delete(id: Facility['id']): Promise<FacilityResponseDto> {
    try {
      return await this.facilityPersistenceService.delete(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't delete completly facility", error.statusCode);
    }
  }

  async findAllForMobile(): Promise<FacilityMobileResponseDto[]> {
    try {
      const facilities = await this.facilityPersistenceService.findAllForMobile();
      const facilitiesWithSignedUrls = await Promise.all(
        facilities.map(async (facility) => {
          const imagePath = await this.supabaseService.getSignedUrl('sabino-zibata', facility.imagePath);

          return {
            ...facility,
            imagePath,
          };
        }),
      );

      return facilitiesWithSignedUrls;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all facilities", error.statusCode);
    }
  }
}
