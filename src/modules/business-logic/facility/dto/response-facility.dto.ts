import { ApiProperty } from '@nestjs/swagger';

export class FacilityResponseDto {
  @ApiProperty({ description: 'Id del usuario', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  id: string;

  @ApiProperty({ description: 'Nombre de la amenidad', example: 'Gimnasio' })
  name: string;

  @ApiProperty({ description: 'Descripción de la amenidad', example: 'Gimnasio para todos los habitantes.' })
  description: string;

  @ApiProperty({ description: 'Fecha de creación', example: '2024-11-15T05:00:14.855Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Fecha de actualización', example: '2024-11-15T05:00:14.855Z' })
  updatedAt: Date;

  open: string;
  close: string;
  imagePath: string;
  reservable: boolean;
  daysOfWeek?: number[];
  startTime?: string;
  endTime?: string;
}
