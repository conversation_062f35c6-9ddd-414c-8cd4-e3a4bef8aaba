import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsOptional, IsNumber } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateFacilityDto {
  @ApiProperty({ description: 'Nombre de la amenidad', example: 'Gimnasio' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Descripción de la amenidad', example: 'Gimnasio para todos los habitantes.' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @IsString()
  @IsOptional()
  imagePath?: string;

  @Transform(({ value }) => value === 'true')
  reservable: boolean;

  @IsOptional()
  @IsString()
  startTime?: string;

  @IsOptional()
  @IsString()
  endTime?: string;

  @IsArray()
  @IsOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map((v) => Number(v));
    }
    if (typeof value === 'string') {
      // Si viene como string "1,2,3"
      return value.split(',').map((v) => Number(v));
    }
    return [];
  })
  daysOfWeek?: number[];

  @IsString()
  @IsNotEmpty()
  open: string;

  @IsString()
  @IsNotEmpty()
  close: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  maxAmountOfPeople?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  maxTimeOfStay?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  maxDateOfReservation?: number;
}
