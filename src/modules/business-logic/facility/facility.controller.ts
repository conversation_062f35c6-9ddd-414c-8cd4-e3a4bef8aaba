import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseUUIDPipe,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FacilityService } from './facility.service';
import { CreateFacilityDto } from './dto/create-facility.dto';
import { UpdateFacilityDto } from './dto/update-facility.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('facility')
export class FacilityController {
  constructor(private readonly facilityService: FacilityService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async create(@Body() createDto: CreateFacilityDto, @UploadedFile() file: Express.Multer.File) {
    return this.facilityService.create(createDto, file);
  }

  @Get()
  findAll() {
    return this.facilityService.findAll();
  }

  @Get('for-select')
  findAllForSelect() {
    return this.facilityService.findAllForSelect();
  }

  @Get(':id')
  findOne(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.facilityService.findOne(id);
  }

  @Patch(':id')
  @UseInterceptors(FileInterceptor('file'))
  update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() updateFacilityDto: UpdateFacilityDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.facilityService.update(id, updateFacilityDto, file);
  }

  @Delete(':id')
  remove(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.facilityService.delete(id);
  }
}
