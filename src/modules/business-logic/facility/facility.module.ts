import { Module } from '@nestjs/common';
import { FacilityService } from './facility.service';
import { FacilityController } from './facility.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';
import { StorageModule } from 'src/modules/storage/storage.module';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [FacilityController],
  providers: [FacilityService],
  exports: [FacilityService],
})
export class FacilityModule {}
