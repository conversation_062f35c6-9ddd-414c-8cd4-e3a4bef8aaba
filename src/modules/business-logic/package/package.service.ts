import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { DeliverPackageDto } from './dto/deliver-package.dto';
import { PackagePersistenceService } from '../../persistence/package-persistence.service';
import { PackageStatus } from '@prisma/client';
import { SupabaseService } from 'src/modules/storage/supabase.service';
import { randomUUID } from 'crypto';

@Injectable()
export class PackageService {
  constructor(
    private readonly packagePersistenceService: PackagePersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async create(data: CreatePackageDto, files: Express.Multer.File[], receivedBy: string) {
    const packageCreated = await this.packagePersistenceService.create(data, receivedBy);
    if (files?.length > 0) {
      files.forEach(async (file, index) => {
        const extension = file.originalname.split('.').pop();
        const path = `packages/${packageCreated.id}-${index + 1}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.packagePersistenceService.saveImagePath(packageCreated.id, uploadedPath);
      });
    }

    return packageCreated;
  }

  async findAll() {
    const packages = await this.packagePersistenceService.findAll();
    for (const packageToFind of packages) {
      if (packageToFind.images.length > 0) {
        packageToFind.images = await Promise.all(
          packageToFind.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }
    return packages;
  }

  async getPackageById(packageId: string) {
    const packageFound = await this.packagePersistenceService.findById(packageId);
    if (packageFound.images.length > 0) {
      packageFound.images = await Promise.all(
        packageFound.images.map(async (image) => {
          const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
          return {
            ...image,
            path: signedUrl,
          };
        }),
      );
    }
    return packageFound;
  }

  async updatePackage(packageId: string, data: UpdatePackageDto, files: Express.Multer.File[] = []) {
    const updatedPackage = await this.packagePersistenceService.update(packageId, data);
    try {
      const parsedImages: { id: string; path: string }[] =
        typeof data.images === 'string' ? JSON.parse(data.images) : (data.images ?? []);

      const imagesToKeep = new Set(parsedImages.map((img) => img.id));
      const current = await this.packagePersistenceService.findById(packageId);

      const imagesToDelete = current.images.filter((img) => !imagesToKeep.has(img.id));

      for (const image of imagesToDelete) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
        await this.packagePersistenceService.deleteImageById(image.id);
      }

      for (const file of files) {
        const extension = file.originalname.split('.').pop();
        const path = `packages/${updatedPackage.id}-${randomUUID()}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.packagePersistenceService.saveImagePath(updatedPackage.id, uploadedPath);
      }

      return updatedPackage;
    } catch (error: any) {
      throw new Error(`Error updating package: ${error.message}`);
    }
  }

  async deletePackage(packageId: string) {
    const packageDeleted = await this.packagePersistenceService.findById(packageId);
    const images = packageDeleted.images;
    for (const image of images) {
      await this.supabaseService.deleteFile('sabino-zibata', image.path);
    }
    return this.packagePersistenceService.delete(packageId);
  }

  async generateDeliveryToken(packageId: string) {
    const packageToUpdate = await this.packagePersistenceService.findById(packageId);
    if (!packageToUpdate) {
      throw new NotFoundException('Paquete no encontrado');
    }
    if (packageToUpdate.status !== PackageStatus.PENDING) {
      throw new BadRequestException('El paquete no está pendiente de entrega');
    }
    const token = randomUUID().substring(0, 6).toUpperCase();
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    return this.packagePersistenceService.generateDeliveryToken(packageId, token, expiresAt);
  }

  async deliverPackage(data: DeliverPackageDto, deliveredBy: string, packageId: string) {
    const packageToDeliver = await this.packagePersistenceService.findById(packageId);
    if (!packageToDeliver) {
      throw new NotFoundException('Paquete no encontrado');
    }
    if (packageToDeliver.status !== PackageStatus.PENDING) {
      throw new BadRequestException('El paquete no está pendiente de entrega');
    }
    if (packageToDeliver.deliveryToken !== data.deliveryToken) {
      throw new BadRequestException('Token de entrega inválido');
    }
    if (packageToDeliver.tokenExpiresAt < new Date()) {
      throw new BadRequestException('El token de entrega ha expirado');
    }
    return this.packagePersistenceService.deliverPackage(packageId, deliveredBy);
  }

  async findByPropertyId(propertyId: string) {
    return this.packagePersistenceService.findByPropertyId(propertyId);
  }

  async findPendingByPropertyId(propertyId: string) {
    return this.packagePersistenceService.findPendingByPropertyId(propertyId);
  }

  async findPending() {
    return this.packagePersistenceService.findPending();
  }
}
