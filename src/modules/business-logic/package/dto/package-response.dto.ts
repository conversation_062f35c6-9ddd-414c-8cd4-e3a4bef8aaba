import { ApiProperty } from '@nestjs/swagger';
import { PackageStatus } from '@prisma/client';

export class PackageResponseDto {
  @ApiProperty({
    description: 'ID único del paquete',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Número de paquete',
    example: 1,
  })
  number: number;

  @ApiProperty({
    description: 'Estado del paquete',
    enum: PackageStatus,
    example: PackageStatus.PENDING,
  })
  status: PackageStatus;

  @ApiProperty({
    description: 'Token de entrega',
    example: 'ABC123',
    required: false,
  })
  deliveryToken?: string;

  @ApiProperty({
    description: 'Fecha de expiración del token',
    example: '2024-04-01T18:00:00.000Z',
    required: false,
  })
  tokenExpiresAt?: Date;

  @ApiProperty({
    description: 'Fecha de recepción en administración',
    example: '2024-04-01T10:00:00.000Z',
  })
  receivedAt: Date;

  @ApiProperty({
    description: 'Fecha de entrega al residente',
    example: '2024-04-01T15:00:00.000Z',
    required: false,
  })
  deliveredAt?: Date;

  @ApiProperty({
    description: 'Notas adicionales',
    example: 'Paquete frágil, manejar con cuidado',
    required: false,
  })
  notes?: string;
}
