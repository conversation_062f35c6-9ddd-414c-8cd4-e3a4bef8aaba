import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseUUIDPipe,
  UseGuards,
  UploadedFiles,
  Req,
  UseInterceptors,
} from '@nestjs/common';
import { PackageService } from './package.service';
import { Request } from 'express';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PackageResponseDto } from './dto/package-response.dto';
import { AuthGuard } from 'src/common/guards/auth/auth.guard';
import { RolesGuard } from 'src/common/guards/auth/roles.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { JwtService } from '@nestjs/jwt';
import { DeliverPackageDto } from './dto/deliver-package.dto';
import { FilesInterceptor } from '@nestjs/platform-express';

@ApiTags('Package')
@UseGuards(AuthGuard, RolesGuard)
@Controller('package')
export class PackageController {
  constructor(
    private readonly packageService: PackageService,
    private readonly jwtService: JwtService,
  ) {}

  @Post()
  @Roles('ADMIN')
  @UseInterceptors(FilesInterceptor('files', 5))
  async create(
    @Body() createPackageDto: CreatePackageDto,
    @UploadedFiles() files: Express.Multer.File[],
    @Req() req: Request,
  ) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.packageService.create(createPackageDto, files, payload.sub);
  }

  @Get()
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Obtener todos los paquetes' })
  @ApiResponse({ status: 200, description: 'Lista de paquetes', type: [PackageResponseDto] })
  findAll() {
    return this.packageService.findAll();
  }

  @Get('pending')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Obtener paquetes pendientes' })
  @ApiResponse({ status: 200, description: 'Lista de paquetes pendientes', type: [PackageResponseDto] })
  findPending() {
    return this.packageService.findPending();
  }

  @Get('property/:propertyId')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Obtener paquetes por propiedad' })
  @ApiResponse({ status: 200, description: 'Lista de paquetes de la propiedad', type: [PackageResponseDto] })
  findByProperty(@Param('propertyId', ParseUUIDPipe) propertyId: string) {
    return this.packageService.findByPropertyId(propertyId);
  }

  @Get('property/:propertyId/pending')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Obtener paquetes pendientes por propiedad' })
  @ApiResponse({ status: 200, description: 'Lista de paquetes pendientes de la propiedad', type: [PackageResponseDto] })
  findPendingByProperty(@Param('propertyId', ParseUUIDPipe) propertyId: string) {
    return this.packageService.findPendingByPropertyId(propertyId);
  }

  @Get(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Obtener un paquete por ID' })
  @ApiResponse({ status: 200, description: 'Paquete encontrado', type: PackageResponseDto })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.packageService.getPackageById(id);
  }

  @Patch(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Actualizar un paquete' })
  @ApiResponse({ status: 200, description: 'Paquete actualizado exitosamente', type: PackageResponseDto })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePackageDto: UpdatePackageDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.packageService.updatePackage(id, updatePackageDto, files);
  }

  @Delete(':id')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Eliminar un paquete' })
  @ApiResponse({ status: 200, description: 'Paquete eliminado exitosamente' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.packageService.deletePackage(id);
  }

  //Deliver package
  @Patch('/:id/deliver')
  @Roles('ADMIN')
  @ApiOperation({ summary: 'Entregar paquete usando token' })
  @ApiResponse({ status: 200, description: 'Paquete entregado exitosamente', type: PackageResponseDto })
  async deliverPackage(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() deliverPackageDto: DeliverPackageDto,
    @Req() req: Request,
  ) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.packageService.deliverPackage(deliverPackageDto, payload.sub, id);
  }
}
