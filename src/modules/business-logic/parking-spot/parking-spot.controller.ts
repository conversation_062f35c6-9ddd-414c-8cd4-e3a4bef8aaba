import { Controller, Get, Post, Body, Patch, Param, Delete, ParseUUIDPipe } from '@nestjs/common';
import { ParkingSpotService } from './parking-spot.service';
import { CreateParkingSpotDto } from './dto/create-parking-spot.dto';
import { UpdateParkingSpotDto } from './dto/update-parking-spot.dto';

@Controller('parking-spot')
export class ParkingSpotController {
  constructor(private readonly parkingSpotService: ParkingSpotService) {}

  @Post()
  create(@Body() createParkingSpotDto: CreateParkingSpotDto) {
    return this.parkingSpotService.create(createParkingSpotDto);
  }

  @Get()
  findAll() {
    return this.parkingSpotService.findAll();
  }

  @Get('visitors')
  async findVisitors() {
    const spots = await this.parkingSpotService.findVisitors();
    return spots;
  }

  @Get('for-select')
  findAllForSelect() {
    return this.parkingSpotService.findAllForSelect();
  }

  @Get('by-property/:propertyId')
  findByProerty(@Param('propertyId', new ParseUUIDPipe()) propertyId: string) {
    return this.parkingSpotService.findByProperty(propertyId);
  }

  @Get(':id')
  findOne(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.parkingSpotService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id', new ParseUUIDPipe()) id: string, @Body() updateParkingSpotDto: UpdateParkingSpotDto) {
    return this.parkingSpotService.update(id, updateParkingSpotDto);
  }

  @Delete(':id')
  remove(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.parkingSpotService.delete(id);
  }
}
