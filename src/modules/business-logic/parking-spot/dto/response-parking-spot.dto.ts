import { ApiProperty } from '@nestjs/swagger';
import { ParkingSpot, ParkingSpotType } from '@prisma/client';

export interface ParkingSpotResponse extends ParkingSpot {}

export class ParkingSpotResponseDto implements ParkingSpotResponse {
  @ApiProperty({ description: 'Id del estacionamiento', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  id: string;

  @ApiProperty({ description: 'Número de estacionamiento', example: 'EVP-1' })
  spotNumber: string;

  @ApiProperty({ description: 'Valor booleano para saber si está disponible el lugar', example: true })
  isAvailable: boolean;

  @ApiProperty({ description: 'Tipo de estacionamiento', example: 'RESIDENT' })
  type: ParkingSpotType;

  @ApiProperty({
    description: 'ID de la propiedad asociada (opcional)',
    example: 'property-id',
  })
  propertyId: string;

  @ApiProperty({ description: 'Fecha de creación', example: '2024-11-15T05:00:14.855Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Fecha de actualización', example: '2024-11-15T05:00:14.855Z' })
  updatedAt: Date;
}
