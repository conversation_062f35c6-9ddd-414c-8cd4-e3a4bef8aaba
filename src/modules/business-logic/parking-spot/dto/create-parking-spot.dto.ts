import { ApiProperty } from '@nestjs/swagger';
import { ParkingSpot, ParkingSpotType } from '@prisma/client';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export interface CreateParkingSpotRequest extends Omit<ParkingSpot, 'id' | 'createdAt' | 'updatedAt'> {}

export class CreateParkingSpotDto implements CreateParkingSpotRequest {
  @ApiProperty({ description: 'Número de estacionamiento', example: 'EVP-1' })
  @IsNotEmpty()
  @IsString()
  spotNumber: string;

  @ApiProperty({ description: 'Disponibilidad del estacionamiento', example: true })
  @IsBoolean()
  isAvailable: boolean;

  @ApiProperty({ description: 'Tipo de estacionamiento', example: 'RESIDENT' })
  @IsString()
  type: ParkingSpotType;

  @ApiProperty({
    description: 'ID de la propiedad asociada (opcional)',
    example: 'property-id',
    required: false,
  })
  @IsString()
  propertyId: string;
}
