import { Injectable } from '@nestjs/common';
import { CreateParkingSpotDto } from './dto/create-parking-spot.dto';
import { UpdateParkingSpotDto } from './dto/update-parking-spot.dto';
import { ParkingSpotPersistenceService } from '../../persistence/parking-spot-persistence.service';
import { ParkingSpot } from '@prisma/client';
import { ParkingSpotResponseDto } from './dto/response-parking-spot.dto';
import { ErrorHandlerService } from 'src/modules/error-handler/error-handler.service';

@Injectable()
export class ParkingSpotService {
  constructor(
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly parkingSpotPersistenceService: ParkingSpotPersistenceService,
  ) {}

  async create(createParkingSpotDto: CreateParkingSpotDto) {
    try {
      return await this.parkingSpotPersistenceService.create(createParkingSpotDto);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't create parking spot", error.statusCode);
    }
  }

  async findAll() {
    try {
      return await this.parkingSpotPersistenceService.findAll();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all parking spots", error.statusCode);
    }
  }

  async findAllForSelect() {
    try {
      return await this.parkingSpotPersistenceService.findAllForSelect();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all parking spots", error.statusCode);
    }
  }

  async findVisitors() {
    try {
      return await this.parkingSpotPersistenceService.findVisitors();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get available parking spots", error.statusCode);
    }
  }

  async findByProperty(propertyId: ParkingSpot['propertyId']) {
    try {
      return await this.parkingSpotPersistenceService.findByProperty(propertyId);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get available porperty parking spots", error.statusCode);
    }
  }

  async findOne(id: ParkingSpot['id']): Promise<ParkingSpotResponseDto> {
    try {
      return await this.parkingSpotPersistenceService.findById(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get parking spot", error.statusCode);
    }
  }

  async update(id: ParkingSpot['id'], updateUserDto: UpdateParkingSpotDto): Promise<ParkingSpotResponseDto> {
    try {
      return await this.parkingSpotPersistenceService.update(id, updateUserDto);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't update parking spot", error.statusCode);
    }
  }

  async delete(id: ParkingSpot['id']): Promise<ParkingSpotResponseDto> {
    try {
      return await this.parkingSpotPersistenceService.delete(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't delete completly parking spot", error.statusCode);
    }
  }
}
