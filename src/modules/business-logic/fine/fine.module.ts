import { Module } from '@nestjs/common';
import { FineService } from './fine.service';
import { FineController } from './fine.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';
import { StorageModule } from 'src/modules/storage/storage.module';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [FineController],
  providers: [FineService],
  exports: [FineService],
})
export class FineModule {}
