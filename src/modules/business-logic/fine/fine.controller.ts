import { Controller, Post, Get, Patch, Delete, Body, Param, UseInterceptors, UploadedFiles } from '@nestjs/common';
import { FineService } from './fine.service';
import { CreateFineDto } from './dto/create-fine.dto';
import { UpdateFineDto } from './dto/update-fine.dto';
import { FilesInterceptor } from '@nestjs/platform-express';

@Controller('fine')
export class FineController {
  constructor(private readonly fineService: FineService) {}

  @Post()
  @UseInterceptors(FilesInterceptor('files', 3))
  async createFine(@Body() createDto: CreateFineDto, @UploadedFiles() files: Express.Multer.File[]) {
    return this.fineService.createFine(createDto, files);
  }

  @Get()
  async findAllFines() {
    return this.fineService.findAllFines();
  }

  @Get('/:fineId')
  async getFineById(@Param('fineId') fineId: string) {
    return this.fineService.getFineById(fineId);
  }

  @Patch('/:fineId')
  async updateFine(
    @Param('fineId') fineId: string,
    @Body() updateDto: UpdateFineDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.fineService.updateFine(fineId, updateDto, files);
  }

  @Delete('/:fineId')
  async deleteFine(@Param('fineId') fineId: string) {
    return this.fineService.deleteFine(fineId);
  }
}
