import { IsString, IsN<PERSON>ber, IsBoolean, IsOptional, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateFineDto {
  @Transform(({ value }) => Number(value))
  @IsNumber()
  amount: number;

  @IsString()
  description: string;

  @IsBoolean()
  @IsOptional()
  isPaid?: boolean;

  @IsDateString()
  @IsOptional()
  paidAt?: Date;

  @IsString()
  propertyId: string;
}
