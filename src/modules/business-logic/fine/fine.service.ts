import { Injectable } from '@nestjs/common';
import { CreateFineDto } from './dto/create-fine.dto';
import { UpdateFineDto } from './dto/update-fine.dto';
import { FinePersistenceService } from 'src/modules/persistence/fine-persistence.service';
import { SupabaseService } from 'src/modules/storage/supabase.service';
import { randomUUID } from 'crypto';

@Injectable()
export class FineService {
  constructor(
    private readonly finePersistence: FinePersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async createFine(data: CreateFineDto, files: Express.Multer.File[]) {
    const fine = await this.finePersistence.createFine(data);
    files.forEach(async (file, index) => {
      const extension = file.originalname.split('.').pop();
      const path = `fines/${fine.id}-${index + 1}.${extension}`;

      const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

      await this.finePersistence.saveImagePath(fine.id, uploadedPath);
    });

    return fine;
  }

  async findAllFines() {
    const fines = await this.finePersistence.findAllFines();
    for (const infraction of fines) {
      if (infraction.images.length > 0) {
        infraction.images = await Promise.all(
          infraction.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }
    return fines;
  }

  async getFineById(fineId: string) {
    const fine = await this.finePersistence.getFineById(fineId);
    if (fine.images.length > 0) {
      fine.images = await Promise.all(
        fine.images.map(async (image) => {
          const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
          return {
            ...image,
            path: signedUrl,
          };
        }),
      );
    }
    return fine;
  }

  async updateFine(fineId: string, data: UpdateFineDto, files: Express.Multer.File[]) {
    const updatedFine = await this.finePersistence.updateFine(fineId, data);
    try {
      const parsedImages: { id: string; path: string }[] =
        typeof data.images === 'string' ? JSON.parse(data.images) : (data.images ?? []);

      const imagesToKeep = new Set(parsedImages.map((img) => img.id));
      const current = await this.finePersistence.getFineById(fineId);

      const imagesToDelete = current.images.filter((img) => !imagesToKeep.has(img.id));

      for (const image of imagesToDelete) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
        await this.finePersistence.deleteImageById(image.id);
      }

      for (const file of files) {
        const extension = file.originalname.split('.').pop();
        const path = `infractions/${updatedFine.id}-${randomUUID()}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.finePersistence.saveImagePath(updatedFine.id, uploadedPath);
      }

      return updatedFine;
    } catch (error: any) {
      throw new Error(`Error updating maintenance issue report: ${error.message}`);
    }
  }

  async deleteFine(fineId: string) {
    return this.finePersistence.deleteFine(fineId);
  }
}
