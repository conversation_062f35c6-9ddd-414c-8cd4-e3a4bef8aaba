import { Module } from '@nestjs/common';
import { MaintenanceFeeService } from './maintenance-fee.service';
import { MaintenanceFeeController } from './maintenance-fee.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';

@Module({
  imports: [PersistenceModule],
  controllers: [MaintenanceFeeController],
  providers: [MaintenanceFeeService],
  exports: [MaintenanceFeeService],
})
export class MaintenanceFeeModule {}
