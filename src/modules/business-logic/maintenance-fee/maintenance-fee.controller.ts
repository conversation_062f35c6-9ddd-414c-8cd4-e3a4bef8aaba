import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { MaintenanceFeeService } from './maintenance-fee.service';
import { CreateMaintenanceFeeDto } from './dto/create-maintenance-fee.dto';

@Controller('maintenance-fee')
export class MaintenanceFeeController {
  constructor(private readonly maintenanceFeeService: MaintenanceFeeService) {}

  @Post()
  create(@Body() createDto: CreateMaintenanceFeeDto) {
    return this.maintenanceFeeService.create(createDto);
  }

  @Get()
  findAll() {
    return this.maintenanceFeeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.maintenanceFeeService.findById(id);
  }
}
