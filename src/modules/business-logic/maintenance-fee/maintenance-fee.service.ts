// maintenance-fee.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { MaintenanceFeePersistenceService } from '../../persistence/maintenance-fee-persistence.service';
import { CreateMaintenanceFeeDto } from './dto/create-maintenance-fee.dto';
import { UpdateMaintenanceFeeDto } from './dto/update-maintenance-fee.dto';

@Injectable()
export class MaintenanceFeeService {
  constructor(private readonly persistence: MaintenanceFeePersistenceService) {}

  async create(dto: CreateMaintenanceFeeDto) {
    return this.persistence.create(dto);
  }

  async findAll() {
    return this.persistence.findAll();
  }

  async findById(id: string) {
    const fee = await this.persistence.findById(id);
    if (!fee) throw new NotFoundException('Maintenance fee not found');
    return fee;
  }

  async update(id: string, dto: UpdateMaintenanceFeeDto) {
    return this.persistence.update(id, dto);
  }

  async delete(id: string) {
    return this.persistence.delete(id);
  }
}
