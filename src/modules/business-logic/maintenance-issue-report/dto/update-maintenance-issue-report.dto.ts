import { PartialType } from '@nestjs/swagger';
import { CreateMaintenanceIssueReportDto } from './create-maintenance-issue-report.dto';
import { IsOptional, IsString } from 'class-validator';
import { MaintenanceIssueImage } from '@prisma/client';

export class UpdateMaintenanceIssueReportDto extends PartialType(CreateMaintenanceIssueReportDto) {
  @IsOptional()
  @IsString({ each: true })
  images?: MaintenanceIssueImage[];
}
