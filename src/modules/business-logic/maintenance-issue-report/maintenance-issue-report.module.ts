import { Module } from '@nestjs/common';
import { MaintenanceIssueReportService } from './maintenance-issue-report.service';
import { MaintenanceIssueReportController } from './maintenance-issue-report.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';
import { StorageModule } from 'src/modules/storage/storage.module';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [MaintenanceIssueReportController],
  providers: [MaintenanceIssueReportService],
  exports: [MaintenanceIssueReportService],
})
export class MaintenanceIssueReportModule {}
