import { Controller, Post, Get, Patch, Delete, Body, Param, UseInterceptors, UploadedFiles } from '@nestjs/common';
import { CreateMaintenanceIssueReportDto } from './dto/create-maintenance-issue-report.dto';
import { UpdateMaintenanceIssueReportDto } from './dto/update-maintenance-issue-report.dto';
import { MaintenanceIssueReportService } from './maintenance-issue-report.service';
import { FilesInterceptor } from '@nestjs/platform-express';

@Controller('maintenance-issue-report')
export class MaintenanceIssueReportController {
  constructor(private readonly maintenanceIssueReportService: MaintenanceIssueReportService) {}

  @Post()
  @UseInterceptors(FilesInterceptor('files', 3))
  async createMaintenanceIssueReport(
    @Body() createDto: CreateMaintenanceIssueReportDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.maintenanceIssueReportService.createMaintenanceIssueReport(createDto, files);
  }

  @Get()
  async findAllMaintenanceIssueReports() {
    return this.maintenanceIssueReportService.findAllMaintenanceIssueReports();
  }

  @Get('/:issueId')
  async getMaintenanceIssueReportById(@Param('issueId') issueId: string) {
    return this.maintenanceIssueReportService.getMaintenanceIssueReportById(issueId);
  }

  @Patch('/:issueId')
  @UseInterceptors(FilesInterceptor('files', 3))
  async updateMaintenanceIssueReport(
    @Param('issueId') issueId: string,
    @Body() updateDto: UpdateMaintenanceIssueReportDto,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    return this.maintenanceIssueReportService.updateMaintenanceIssueReport(issueId, updateDto, files);
  }

  @Delete('/:issueId')
  async deleteMaintenanceIssueReport(@Param('issueId') issueId: string) {
    return this.maintenanceIssueReportService.deleteMaintenanceIssueReport(issueId);
  }
}
