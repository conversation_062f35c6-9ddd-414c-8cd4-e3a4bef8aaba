import { Controller, Post, Get, Patch, Delete, Body, Param, ParseUUIDPipe } from '@nestjs/common';
import { ServiceService } from './service.service';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Service')
@Controller('service')
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @ApiOperation({ summary: 'Registrar un nuevo servicio' })
  @ApiResponse({ status: 201, description: 'Servicio creado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateServiceDto) {
    return this.serviceService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los servicios' })
  @ApiResponse({ status: 200, description: 'Lista de servicios disponibles' })
  @Get()
  async findAll() {
    return this.serviceService.findAll();
  }

  @Get('for-select')
  findAllForSelect() {
    return this.serviceService.findAllForSelect();
  }

  @ApiOperation({ summary: 'Obtener un servicio por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del servicio' })
  @Get('/:serviceId')
  async findById(@Param('serviceId', new ParseUUIDPipe()) serviceId: string) {
    return this.serviceService.findById(serviceId);
  }

  @ApiOperation({ summary: 'Actualizar un servicio' })
  @ApiResponse({ status: 200, description: 'Servicio actualizado exitosamente' })
  @Patch('/:serviceId')
  async update(@Param('serviceId', new ParseUUIDPipe()) serviceId: string, @Body() updateDto: UpdateServiceDto) {
    return this.serviceService.update(serviceId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un servicio' })
  @ApiResponse({ status: 200, description: 'Servicio eliminado exitosamente' })
  @Delete('/:serviceId')
  async delete(@Param('serviceId', new ParseUUIDPipe()) serviceId: string) {
    return this.serviceService.delete(serviceId);
  }
}
