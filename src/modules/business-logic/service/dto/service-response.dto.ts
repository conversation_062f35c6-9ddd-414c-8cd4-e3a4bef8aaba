import { ApiProperty } from '@nestjs/swagger';

export class ServiceResponseDto {
  @ApiProperty({
    description: 'ID único del servicio',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nombre del servicio',
    example: 'Mantenimiento de Jardines',
  })
  name: string;

  @ApiProperty({
    description: 'Descripción del servicio',
    example: 'Servicio de mantenimiento de jardines y áreas verdes',
  })
  description?: string;

  @ApiProperty({
    description: 'Costo del servicio',
    example: 500.0,
  })
  cost?: number;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
