import { IsString, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateServiceDto {
  @ApiProperty({
    description: 'Nombre del servicio',
    example: 'Mantenimiento de Jardines',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Descripción del servicio',
    example: 'Servicio de mantenimiento de jardines y áreas verdes',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Costo del servicio',
    example: 500.0,
  })
  @IsOptional()
  @IsNumber()
  cost?: number;

  @IsOptional()
  @IsString()
  supplierId?: string;
}
