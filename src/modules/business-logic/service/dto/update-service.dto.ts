import { PartialType } from '@nestjs/mapped-types';
import { CreateServiceDto } from './create-service.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString } from 'class-validator';

export class UpdateServiceDto extends PartialType(CreateServiceDto) {
  @ApiProperty({
    description: 'Costo actualizado del servicio',
    example: 600.0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  cost?: number;

  @ApiProperty({
    description: 'Descripción actualizada del servicio',
    example: 'Mantenimiento general de jardines con poda y fertilización',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
