import { Injectable } from '@nestjs/common';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { ServicePersistenceService } from '../../persistence/service-persistence.service';

@Injectable()
export class ServiceService {
  constructor(private readonly servicePersistence: ServicePersistenceService) {}

  async create(data: CreateServiceDto) {
    return this.servicePersistence.create(data);
  }

  async findAll() {
    return this.servicePersistence.findAll();
  }

  async findAllForSelect() {
    return await this.servicePersistence.findAllForSelect();
  }

  async findById(serviceId: string) {
    return this.servicePersistence.findById(serviceId);
  }

  async update(serviceId: string, data: UpdateServiceDto) {
    return this.servicePersistence.update(serviceId, data);
  }

  async delete(serviceId: string) {
    return this.servicePersistence.delete(serviceId);
  }
}
