import { ApiProperty } from '@nestjs/swagger';
import { Vehicle } from '@prisma/client';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export interface CreateVehicleRequest extends Omit<Vehicle, 'id' | 'createdAt' | 'updatedAt'> {}

export class CreateVehicleDto implements CreateVehicleRequest {
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ description: 'Matrícula del vehículo', example: 'ABC-123' })
  @IsNotEmpty()
  @IsString()
  plate: string;

  @ApiProperty({ description: 'Marca del vehículo', example: 'Toyota' })
  @IsNotEmpty()
  @IsString()
  brand: string;

  @ApiProperty({ description: 'Modelo del vehículo', example: 'Corolla' })
  @IsNotEmpty()
  @IsString()
  model: string;

  @ApiProperty({ description: 'Color del vehículo', example: 'Rojo' })
  @IsNotEmpty()
  @IsString()
  color: string;

  @ApiProperty({ description: 'Descripción del vehículo', example: 'Sedán rojo' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'ID de la propiedad asociada',
    example: 'property-id',
    required: false,
  })
  @IsString()
  propertyId: string;
}
