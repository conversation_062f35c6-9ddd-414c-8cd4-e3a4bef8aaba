import { PartialType } from '@nestjs/mapped-types';
import { CreateVehicleDto } from './create-vehicle.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateVehicleDto extends PartialType(CreateVehicleDto) {
  @ApiProperty({
    description: 'Nueva descripción del vehículo',
    example: 'Honda Civic 2023, Azul',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
