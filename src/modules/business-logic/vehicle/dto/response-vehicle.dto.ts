import { ApiProperty } from '@nestjs/swagger';

export class VehicleResponseDto {
  @ApiProperty({
    description: 'ID único del vehículo',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Número de placa del vehículo',
    example: 'ABC-1234',
  })
  plate: string;

  @ApiProperty({
    description: 'Descripción del vehículo',
    example: 'Toyota Corolla 2022, Rojo',
  })
  description?: string;

  @ApiProperty({
    description: 'ID de la propiedad asociada',
    example: 'property-uuid',
  })
  propertyId: string;

  @ApiProperty({
    description: 'Fecha de creación del vehículo',
    example: '2024-04-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización del vehículo',
    example: '2024-04-10T12:00:00.000Z',
  })
  updatedAt: Date;
}
