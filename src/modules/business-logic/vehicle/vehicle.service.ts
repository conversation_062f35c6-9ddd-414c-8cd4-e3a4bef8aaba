import { Injectable } from '@nestjs/common';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { VehiclePersistenceService } from 'src/modules/persistence/vehicle-persistence.service';

@Injectable()
export class VehicleService {
  constructor(private readonly vehiclePersistence: VehiclePersistenceService) {}

  async create(data: CreateVehicleDto) {
    return this.vehiclePersistence.create(data);
  }

  async findAll() {
    return this.vehiclePersistence.findAll();
  }

  async findById(vehicleId: string) {
    return this.vehiclePersistence.findById(vehicleId);
  }

  async update(vehicleId: string, data: UpdateVehicleDto) {
    return this.vehiclePersistence.update(vehicleId, data);
  }

  async delete(vehicleId: string) {
    return this.vehiclePersistence.delete(vehicleId);
  }
}
