import { Controller, Post, Get, Delete, Body, Param, ParseUUIDPipe, Patch } from '@nestjs/common';
import { VehicleService } from './vehicle.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Vehicle')
@Controller('vehicle')
export class VehicleController {
  constructor(private readonly vehicleService: VehicleService) {}

  @ApiOperation({ summary: 'Registrar un nuevo vehículo' })
  @ApiResponse({ status: 201, description: 'Vehículo creado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateVehicleDto) {
    return this.vehicleService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los vehículos' })
  @ApiResponse({ status: 200, description: 'Lista de vehículos registrados' })
  @Get()
  async findAll() {
    return this.vehicleService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un vehículo por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del vehículo' })
  @Get('/:vehicleId')
  async findById(@Param('vehicleId', new ParseUUIDPipe()) vehicleId: string) {
    return this.vehicleService.findById(vehicleId);
  }

  @ApiOperation({ summary: 'Actualizar un vehículo' })
  @Patch('/:vehicleId')
  async update(@Param('vehicleId', new ParseUUIDPipe()) vehicleId: string, @Body() updateDto: UpdateVehicleDto) {
    return this.vehicleService.update(vehicleId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un vehículo' })
  @Delete('/:vehicleId')
  async delete(@Param('vehicleId', new ParseUUIDPipe()) vehicleId: string) {
    return this.vehicleService.delete(vehicleId);
  }
}
