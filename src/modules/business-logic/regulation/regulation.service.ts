import { Injectable } from '@nestjs/common';
import { CreateRegulationDto } from './dto/create-regulation.dto';
import { UpdateRegulationDto } from './dto/update-regulation.dto';
import { RegulationPersistenceService } from '../../persistence/regulation-persistence.service';
import { ExceptionsHandler } from '@nestjs/core/exceptions/exceptions-handler';
import { CloudinaryService } from 'src/modules/storage/cloudinary.sevice';
import { SupabaseService } from '../../storage/supabase.service';

@Injectable()
export class RegulationService {
  constructor(
    private readonly regulationPersistence: RegulationPersistenceService,
    private readonly cloudinaryService: CloudinaryService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async create(createRegulationDto: CreateRegulationDto, file?: Express.Multer.File) {
    let fileUrl = null;

    // Si hay un archivo, súbelo a Cloudinary

    try {
      if (file) {
        const buffer = file.buffer;
        fileUrl = await this.supabaseService.uploadFile(
          'sabino-zibata',
          `${createRegulationDto.title}.pdf`,
          buffer,
          file.mimetype,
        );
      }
      return this.regulationPersistence.create({
        ...createRegulationDto,
        imageUrl: fileUrl,
      });
    } catch (error) {
      console.log(error);
      throw ExceptionsHandler;
    }
  }

  async findAll() {
    return this.regulationPersistence.findAll();
  }

  async findById(regulationId: string) {
    return this.regulationPersistence.findById(regulationId);
  }

  async update(regulationId: string, data: UpdateRegulationDto) {
    return this.regulationPersistence.update(regulationId, data);
  }

  async delete(regulationId: string) {
    const deletedRegulation = await this.regulationPersistence.delete(regulationId);
    if (deletedRegulation.fileUrl) {
      await this.cloudinaryService.deleteFile(deletedRegulation.fileUrl);
    }
    return deletedRegulation;
  }
}
