import { ApiProperty } from '@nestjs/swagger';
import { RegulationType } from '@prisma/client';

export class RegulationResponseDto {
  @ApiProperty({
    description: 'ID único del reglamento',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Título del reglamento',
    example: 'Reglamento de uso de la piscina',
  })
  title: string;

  @ApiProperty({
    description: 'Contenido completo del reglamento',
    example: 'Este reglamento indica las reglas a seguir para el uso adecuado de la piscina...',
  })
  content: string;

  @ApiProperty({
    description: 'Tipo de reglamento',
    example: 'AMENITY',
    enum: RegulationType,
  })
  type: RegulationType;

  @ApiProperty({
    description: 'ID de la instalación asociada (opcional)',
    example: 'facility-uuid',
  })
  facilityId?: string;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-02-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-02-10T15:30:00.000Z',
  })
  updatedAt: Date;
}
