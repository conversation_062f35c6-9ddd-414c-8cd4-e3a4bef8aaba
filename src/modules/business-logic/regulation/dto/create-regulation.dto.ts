import { IsString, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RegulationType } from '@prisma/client';

export class CreateRegulationDto {
  @ApiProperty({
    description: 'Título del reglamento',
    example: 'Reglamento de uso de la piscina',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Contenido completo del reglamento',
    example: 'Este reglamento indica las reglas a seguir para el uso adecuado de la piscina...',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Tipo de reglamento',
    example: 'AMENITY',
    enum: RegulationType,
  })
  @IsEnum(RegulationType)
  type: RegulationType;

  @ApiProperty({
    description: 'ID de la instalación asociada (opcional)',
    example: 'facility-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  facilityId?: string;

  @IsOptional()
  @IsString()
  imageUrl?: string;
}
