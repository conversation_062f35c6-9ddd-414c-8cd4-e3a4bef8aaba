import { Controller, Post, Get, Patch, Delete, Body, Param, UseInterceptors, UploadedFile } from '@nestjs/common';
import { RegulationService } from './regulation.service';
import { CreateRegulationDto } from './dto/create-regulation.dto';
import { UpdateRegulationDto } from './dto/update-regulation.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Regulation')
@Controller('regulation')
export class RegulationController {
  constructor(private readonly regulationService: RegulationService) {}

  @ApiOperation({ summary: 'Registrar un reglamento' })
  @ApiResponse({ status: 201, description: 'Reglamento registrado exitosamente' })
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async create(@Body() createDto: CreateRegulationDto, @UploadedFile() file: Express.Multer.File) {
    return this.regulationService.create(createDto, file);
  }

  @ApiOperation({ summary: 'Obtener todos los reglamentos' })
  @ApiResponse({ status: 200, description: 'Lista de reglamentos' })
  @Get()
  async findAll() {
    return this.regulationService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un reglamento por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del reglamento' })
  @Get('/:regulationId')
  async findById(@Param('regulationId') regulationId: string) {
    return this.regulationService.findById(regulationId);
  }

  @ApiOperation({ summary: 'Actualizar un reglamento' })
  @ApiResponse({ status: 200, description: 'Reglamento actualizado exitosamente' })
  @Patch('/:regulationId')
  async update(@Param('regulationId') regulationId: string, @Body() updateDto: UpdateRegulationDto) {
    return this.regulationService.update(regulationId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un reglamento' })
  @ApiResponse({ status: 200, description: 'Reglamento eliminado exitosamente' })
  @Delete('/:regulationId')
  async delete(@Param('regulationId') regulationId: string) {
    return this.regulationService.delete(regulationId);
  }
}
