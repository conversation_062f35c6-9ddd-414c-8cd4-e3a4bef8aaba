import { Module } from '@nestjs/common';
import { RegulationService } from './regulation.service';
import { RegulationController } from './regulation.controller';
import { StorageModule } from 'src/modules/storage/storage.module';
import { CloudinaryService } from 'src/modules/storage/cloudinary.sevice';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [RegulationController],
  providers: [RegulationService, CloudinaryService],
  exports: [RegulationService],
})
export class RegulationModule {}
