import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { RentalService } from './rental.service';
import { CreateRentalDto } from './dto/create-rental.dto';
import { UpdateRentalDto } from './dto/update-rental.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Rental')
@Controller('rental')
export class RentalController {
  constructor(private readonly rentalService: RentalService) {}

  @ApiOperation({ summary: 'Registrar un alquiler' })
  @ApiResponse({ status: 201, description: 'Alquiler registrado exitosamente' })
  @Post()
  async create(@Body() createDto: CreateRentalDto) {
    return this.rentalService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener todos los alquileres' })
  @ApiResponse({ status: 200, description: 'Lista de alquileres' })
  @Get()
  async findAll() {
    return this.rentalService.findAll();
  }

  @ApiOperation({ summary: 'Obtener un alquiler por ID' })
  @ApiResponse({ status: 200, description: 'Detalles del alquiler' })
  @Get('/:rentalId')
  async findById(@Param('rentalId') rentalId: string) {
    return this.rentalService.findById(rentalId);
  }

  @ApiOperation({ summary: 'Actualizar un alquiler' })
  @ApiResponse({ status: 200, description: 'Alquiler actualizado exitosamente' })
  @Patch('/:rentalId')
  async update(@Param('rentalId') rentalId: string, @Body() updateDto: UpdateRentalDto) {
    return this.rentalService.update(rentalId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar un alquiler' })
  @ApiResponse({ status: 200, description: 'Alquiler eliminado exitosamente' })
  @Delete('/:rentalId')
  async delete(@Param('rentalId') rentalId: string) {
    return this.rentalService.delete(rentalId);
  }
}
