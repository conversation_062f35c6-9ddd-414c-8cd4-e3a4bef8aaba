import { Injectable } from '@nestjs/common';
import { CreateRentalDto } from './dto/create-rental.dto';
import { UpdateRentalDto } from './dto/update-rental.dto';
import { RentalPersistenceService } from '../../persistence/rental-persistence.service';

@Injectable()
export class RentalService {
  constructor(private readonly rentalPersistence: RentalPersistenceService) {}

  async create(data: CreateRentalDto) {
    return this.rentalPersistence.create(data);
  }

  async findAll() {
    return this.rentalPersistence.findAll();
  }

  async findById(rentalId: string) {
    return this.rentalPersistence.findById(rentalId);
  }

  async update(rentalId: string, data: UpdateRentalDto) {
    return this.rentalPersistence.update(rentalId, data);
  }

  async delete(rentalId: string) {
    return this.rentalPersistence.delete(rentalId);
  }
}
