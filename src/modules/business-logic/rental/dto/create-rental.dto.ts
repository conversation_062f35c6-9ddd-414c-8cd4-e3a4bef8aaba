import { IsUUID, Is<PERSON>ptional, IsN<PERSON>ber, IsEnum, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { RentalStatus } from '@prisma/client';

export class CreateRentalDto {
  @ApiProperty({
    description: 'ID de la propiedad en renta',
    example: 'property-uuid',
  })
  @IsUUID()
  propertyId: string;

  @ApiProperty({
    description: 'Fecha de inicio del alquiler',
    example: '2024-03-01T00:00:00.000Z',
  })
  @IsDateString()
  startDate: Date;

  @ApiProperty({
    description: 'Fecha de finalización del alquiler (opcional)',
    example: '2025-03-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @ApiProperty({
    description: 'Monto mensual del alquiler',
    example: 1200.5,
  })
  @IsNumber()
  monthlyRate: number;

  @ApiProperty({
    description: 'Estado del alquiler',
    example: 'PENDING_APPROVAL',
    enum: RentalStatus,
  })
  @IsEnum(RentalStatus)
  status: RentalStatus;
}
