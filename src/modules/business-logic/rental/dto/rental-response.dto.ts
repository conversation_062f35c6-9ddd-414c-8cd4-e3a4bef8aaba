import { ApiProperty } from '@nestjs/swagger';
import { RentalStatus } from '@prisma/client';

export class RentalResponseDto {
  @ApiProperty({
    description: 'ID único del alquiler',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID de la propiedad en renta',
    example: 'property-uuid',
  })
  propertyId: string;

  @ApiProperty({
    description: 'Fecha de inicio del alquiler',
    example: '2024-03-01T00:00:00.000Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Fecha de finalización del alquiler (opcional)',
    example: '2025-03-01T00:00:00.000Z',
  })
  endDate?: Date;

  @ApiProperty({
    description: '<PERSON>o mensual del alquiler',
    example: 1200.5,
  })
  monthlyRate: number;

  @ApiProperty({
    description: 'Estado del alquiler',
    example: 'ACTIVE',
    enum: RentalStatus,
  })
  status: RentalStatus;

  @ApiProperty({
    description: 'Fecha de creación',
    example: '2024-02-01T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-03-15T10:00:00.000Z',
  })
  updatedAt: Date;
}
