import { Controller, Get, Patch, Param, Body, Query, ParseUUIDPipe } from '@nestjs/common';
import { MonthlyMaintenanceChargeService } from './monthly-maintenance-charge.service';
import { UpdateMonthlyMaintenanceChargeDto } from './dto/uptate-monthly-maintenance-charge.dto';
import { MarkAsPaidDto } from './dto/mark-as-paid.dto';

@Controller('monthly-maintenance-charge')
export class MonthlyMaintenanceChargeController {
  constructor(private readonly monthlyService: MonthlyMaintenanceChargeService) {}

  @Get()
  findAll(@Query('month') month?: string, @Query('year') year?: string) {
    return this.monthlyService.findAll({
      month: month ? parseInt(month) : undefined,
      year: year ? parseInt(year) : undefined,
    });
  }

  @Patch('mark-as-paid')
  async markAsPaid(@Body() body: MarkAsPaidDto) {
    return this.monthlyService.markAsPaid(body.id);
  }

  @Get(':id')
  findOne(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.monthlyService.findById(id);
  }

  @Patch(':id')
  update(@Param('id', new ParseUUIDPipe()) id: string, @Body() updateDto: UpdateMonthlyMaintenanceChargeDto) {
    return this.monthlyService.update(id, updateDto);
  }
}
