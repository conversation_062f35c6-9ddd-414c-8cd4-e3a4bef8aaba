import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateMonthlyMaintenanceChargeDto } from './dto/create-monthly-maintenance-charge.dto';
import { MonthlyMaintenanceChargePersistenceService } from '../../persistence/monthly-maintenance-charge-persistence.service';
import { UpdateMonthlyMaintenanceChargeDto } from './dto/uptate-monthly-maintenance-charge.dto';

@Injectable()
export class MonthlyMaintenanceChargeService {
  constructor(
    private readonly monthlyMaintenanceChargePersistenceService: MonthlyMaintenanceChargePersistenceService,
  ) {}

  async create(data: CreateMonthlyMaintenanceChargeDto) {
    return this.monthlyMaintenanceChargePersistenceService.create(data);
  }

  async findAll(filters?: { month?: number; year?: number }) {
    return this.monthlyMaintenanceChargePersistenceService.findAll(filters);
  }

  async findById(id: string) {
    const charge = await this.monthlyMaintenanceChargePersistenceService.findById(id);
    if (!charge) {
      throw new NotFoundException('Monthly maintenance charge not found');
    }
    return charge;
  }

  async update(id: string, data: UpdateMonthlyMaintenanceChargeDto) {
    return this.monthlyMaintenanceChargePersistenceService.update(id, data);
  }

  async delete(id: string) {
    return this.monthlyMaintenanceChargePersistenceService.delete(id);
  }

  async markAsPaid(id: string) {
    return this.monthlyMaintenanceChargePersistenceService.markAsPaid(id);
  }
}
