import { Module } from '@nestjs/common';
import { MonthlyMaintenanceChargeService } from './monthly-maintenance-charge.service';
import { MonthlyMaintenanceChargeController } from './monthly-maintenance-charge.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';

@Module({
  imports: [PersistenceModule],
  controllers: [MonthlyMaintenanceChargeController],
  providers: [MonthlyMaintenanceChargeService],
  exports: [MonthlyMaintenanceChargeService],
})
export class MonthlyMaintenanceChargeModule {}
