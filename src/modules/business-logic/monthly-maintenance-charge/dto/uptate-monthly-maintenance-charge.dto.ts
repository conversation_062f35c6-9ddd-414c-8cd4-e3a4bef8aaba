import { IsBoolean, IsDateString, Is<PERSON><PERSON>ber, IsOptional, IsString } from 'class-validator';

export class UpdateMonthlyMaintenanceChargeDto {
  @IsBoolean()
  @IsOptional()
  isPaid?: boolean;

  @IsDateString()
  @IsOptional()
  paidAt?: string;

  @IsBoolean()
  @IsOptional()
  lateFeeApplied?: boolean;

  @IsNumber()
  @IsOptional()
  lateFeeAmount?: number;

  @IsBoolean()
  @IsOptional()
  waivedLateFee?: boolean;

  @IsString()
  @IsOptional()
  paymentId?: string;
}
