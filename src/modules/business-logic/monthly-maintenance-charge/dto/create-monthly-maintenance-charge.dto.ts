import { IsBoolean, IsDateString, IsInt, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CreateMonthlyMaintenanceChargeDto {
  @IsString()
  propertyId: string;

  @IsString()
  maintenanceFeeId: string;

  @IsInt()
  month: number;

  @IsInt()
  year: number;

  @IsDateString()
  dueDate: string;

  @IsBoolean()
  @IsOptional()
  isPaid?: boolean;

  @IsDateString()
  @IsOptional()
  paidAt?: string;

  @IsBoolean()
  @IsOptional()
  lateFeeApplied?: boolean;

  @IsNumber()
  @IsOptional()
  lateFeeAmount?: number;

  @IsBoolean()
  @IsOptional()
  waivedLateFee?: boolean;

  @IsString()
  @IsOptional()
  paymentId?: string;
}
