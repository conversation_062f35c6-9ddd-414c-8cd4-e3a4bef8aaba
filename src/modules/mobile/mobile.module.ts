import { Module } from '@nestjs/common';
import { MobileService } from './mobile.service';
import { MobileController } from './mobile.controller';
import { ReservationService } from '../business-logic/reservation/reservation.service';
import { ComplaintService } from '../business-logic/complaint/complaint.service';
import { UserService } from '../business-logic/user/user.service';
import { PersistenceModule } from '../persistence/persistence.module';
import { MailService } from '../mail/mail.service';
import { FacilityService } from '../business-logic/facility/facility.service';
import { SupabaseService } from '../storage/supabase.service';
import { MaintenanceIssueReportService } from '../business-logic/maintenance-issue-report/maintenance-issue-report.service';
import { PhoneDirectoryService } from '../business-logic/phone-directory/phone-directory.service';
import { StorageModule } from '../storage/storage.module';
import { PropertyService } from '../business-logic/property/property.service';
import { PackageService } from '../business-logic/package/package.service';
import { FineService } from '../business-logic/fine/fine.service';
import { InfractionService } from '../business-logic/infraction/infraction.service';

@Module({
  imports: [PersistenceModule, StorageModule],
  controllers: [MobileController],
  providers: [
    MailService,
    UserService,
    MobileService,
    ComplaintService,
    ReservationService,
    MaintenanceIssueReportService,
    FacilityService,
    SupabaseService,
    PhoneDirectoryService,
    ComplaintService,
    PropertyService,
    PackageService,
    FineService,
    InfractionService,
  ],
})
export class MobileModule {}
