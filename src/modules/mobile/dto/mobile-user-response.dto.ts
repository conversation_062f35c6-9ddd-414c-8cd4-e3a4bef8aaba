import { ApiProperty } from '@nestjs/swagger';
import { AnnouncementImage } from '@prisma/client';

export class MobileUserResponseDto {
  @ApiProperty({ description: 'Id del usuario', example: '887b2eda-6898-49c4-aac5-e73ac188cb7f' })
  id: string;

  @ApiProperty({ description: 'Email del usuario', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ description: 'Nombre del usuario', example: 'Juan' })
  firstName: string;

  @ApiProperty({ description: 'Apellido paterno del usuario', example: '<PERSON>' })
  paternalLastName: string;

  @ApiProperty({ description: 'Apellido materno del usuario', example: '<PERSON>' })
  maternalLastName: string;

  @ApiProperty({ description: 'Teléfono del usuario', example: '5544332211' })
  phone: string;

  announcements: PartialAnnouncements[];

  properties: PartialProperty[];
}

class PartialAnnouncements {
  id: string;
  title: string;
  message: string;
  images: AnnouncementImage[];
  imageUrl: string;
  createdAt: Date;
  role: PartialRole;
}

class PartialRole {
  name: string;
}

class PartialProperty {
  id: string;
  address: string;
  type: string;
}

// class PartialReservation {
//   id: Reservation['id'];
//   status: Reservation['status'];
//   startDateTime: Reservation['startDateTime'];
//   facility: PartialFacility;
// }

// class PartialFacility {
//   name: string;
// }

// class PartialMaintenanceIssueReport {
//   id: MaintenanceIssueReport['id'];
//   description: MaintenanceIssueReport['description'];
//   status: MaintenanceIssueReport['status'];
//   createdAt: MaintenanceIssueReport['createdAt'];
// }

// class PartialComplaint {
//   id: Complaint['id'];
//   detail: Complaint['detail'];
//   priority: Complaint['priority'];
// }

// class PartialFine {
//   id: Fine['id'];
//   amount: Fine['amount'];
//   paidAt: Fine['paidAt'];
// }

// class PartialInfraction {
//   id: Infraction['id'];
//   description: Infraction['description'];
//   date: Infraction['date'];
//   severity: Infraction['severity'];
// }
