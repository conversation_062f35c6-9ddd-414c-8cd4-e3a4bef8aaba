import { VisitMehtod } from '@prisma/client';
import { IsString, IsOptional, IsDateString, IsNotEmpty, IsUUID } from 'class-validator';

export class CreateMobileVisitDto {
  @IsString()
  propertyId: string;

  @IsString()
  visitorName: string;

  @IsString()
  @IsNotEmpty()
  visitMethod: VisitMehtod;

  @IsOptional()
  @IsString()
  vehiclePlate?: string;

  @IsOptional()
  @IsString()
  parkingSpotId?: string;

  @IsDateString()
  schedule: Date;

  @IsOptional()
  @IsDateString()
  checkInTime?: Date;

  @IsOptional()
  @IsDateString()
  checkOutTime?: Date;

  @IsUUID()
  requestedBy: string;
}
