import { IsUUID, IsE<PERSON>, IsN<PERSON>ber, IsDateString, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ReservationStatus } from '@prisma/client';

export class CreateMobileReservationDto {
  @ApiProperty({
    description: 'ID de la propiedad que realiza la reservación',
    example: 'property-uuid',
  })
  @IsUUID()
  propertyId: string;

  @ApiProperty({
    description: 'ID de la instalación reservada',
    example: 'facility-uuid',
  })
  @IsUUID()
  facilityId: string;

  @IsNumber()
  amountOfPeople: number;

  @IsDateString()
  startDateTime: Date;

  @IsDateString()
  endDateTime: Date;

  @ApiProperty({
    description: 'Estado de la reserva',
    example: 'PENDING',
    enum: ReservationStatus,
  })
  @IsOptional()
  @IsEnum(ReservationStatus)
  status?: ReservationStatus;

  @IsOptional()
  @IsUUID()
  requestedBy?: string;

  @IsOptional()
  @IsUUID()
  authorizedBy?: string;

  @IsOptional()
  @IsUUID()
  deniedBy?: string;

  @IsOptional()
  @IsString()
  deniedReason?: string;
}
