import { Injectable } from '@nestjs/common';
import { v2 as cloudinary } from 'cloudinary';
import { StorageService } from './storage.service';

@Injectable()
export class CloudinaryService implements StorageService {
  constructor() {
    cloudinary.config({
      cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
      api_key: process.env.CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
    });
  }

  async uploadFile(
    file: Express.Multer.File,
    type: 'image' | 'video' | 'raw' | 'auto',
    folder: string,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder,
          resource_type: type,
        },
        (error, result) => {
          if (error) {
            reject(new Error(error.message || 'Error uploading file to Cloudinary'));
          } else {
            resolve(result.secure_url);
          }
        },
      );

      uploadStream.end(file.buffer);
    });
  }

  async deleteFile(fileUrl: string): Promise<void> {
    const publicId = fileUrl.split('/').pop()?.split('.')[0]; // Extrae el publicId del archivo
    if (publicId) {
      await cloudinary.uploader.destroy(publicId);
    }
  }
}
