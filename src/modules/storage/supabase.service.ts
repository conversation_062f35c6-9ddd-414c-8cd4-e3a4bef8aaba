// supabase-storage.service.ts
import { Injectable } from '@nestjs/common';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private readonly supabase: SupabaseClient;

  constructor() {
    this.supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);
  }

  async getPublicUrl(bucket: string, path: string): Promise<string> {
    try {
      const publicImageUrl = this.supabase.storage.from(bucket).getPublicUrl(path).data.publicUrl;
      return publicImageUrl;
    } catch (error) {
      console.log(error);
      return '';
    }
  }

  async uploadFile(bucket: string, path: string, fileBuffer: Buffer, contentType: string): Promise<string> {
    const { error } = await this.supabase.storage.from(bucket).upload(path, fileBuffer, {
      contentType,
      upsert: true,
    });

    if (error) {
      // throw new Error(`Upload failed: ${error.message}`);
      console.log(`Upload failed: ${error.message}`);
    }

    return path;
  }

  async deleteFile(bucket: string, path: string): Promise<void> {
    const { error } = await this.supabase.storage.from(bucket).remove([path]);

    if (error) {
      throw new Error(`Error deleting file: ${error.message}`);
    }
  }

  async getSignedUrl(bucket: string, path: string, expiresInSeconds = 300): Promise<string> {
    try {
      const { data, error } = await this.supabase.storage.from(bucket).createSignedUrl(path, expiresInSeconds);

      if (error || !data?.signedUrl) {
        throw new Error(`Error getting signed URL: ${error?.message}`);
      }

      return data.signedUrl;
    } catch (error) {
      console.log(error);
      return '';
    }
  }

  // async getAccessibleUrl(bucket: string, path: string): Promise<string> {
  //   const publicUrl = await this.getPublicUrl(bucket, path);

  //   if (publicUrl && publicUrl.includes('/object/public/')) {
  //     return publicUrl;
  //   }

  //   return await this.getSignedUrl(bucket, path);
  // }
}
