import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ErrorHandlerService {
  constructor(private readonly logger: Logger) {}

  handleError(error: unknown, errorMessage: string, statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR) {
    if (error instanceof HttpException) {
      throw error;
    }
    this.logger.error(error);
    throw new HttpException(errorMessage, statusCode);
  }
}
