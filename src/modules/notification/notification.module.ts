import { Module } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { MailModule } from '../mail/mail.module';
import { PushModule } from '../push/push.module';

@Module({
  imports: [MailModule, PushModule],
  controllers: [NotificationController],
  providers: [NotificationService],
})
export class NotificationModule {}
