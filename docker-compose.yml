version: '3.8'

services:
  postgres:
    image: postgres:13
    container_name: hoams_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: hoams
    ports:
      - '5433:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - hoams_network

volumes:
  postgres_data:
    driver: local

networks:
  hoams_network:
    driver: bridge
