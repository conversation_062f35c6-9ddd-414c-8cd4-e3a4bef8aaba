module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin', 'prettier'],
  extends: ['plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended', 'plugin:prettier/recommended'],
  root: true,
  env: {
    node: true,
    jest: true,
    browser: true,
    es2021: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'prettier/prettier': 'error', // Muestra errores si el código no sigue las reglas de Prettier
    '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }], // Advertencia para variables no usadas (ignora las que empiezan con "_")
    '@typescript-eslint/no-explicit-any': 'error', // Advierte sobre el uso de `any`
    '@typescript-eslint/ban-ts-comment': 'warn', // Advierte sobre el uso de comentarios @ts-ignore
  },
};
